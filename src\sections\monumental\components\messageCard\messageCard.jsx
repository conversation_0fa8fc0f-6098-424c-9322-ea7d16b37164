import { Button, Grid, Typography } from "@mui/material"
import { DatePipe } from "../../pipes/date-pipe"
import PropTypes from "prop-types"
import { useDynamicFontSize } from "../../custom-hooks/dinamic-font-size"

const MessageCard = ({
  msg_id,
  msg_date,
  msg_content,
  handleApproveMessage,
  handleRejectMessage,
  noActions,
}) => {
  const { ref, fontSize } = useDynamicFontSize(3, 16, 18)
  return (
    <Grid
      item
      container
      direction={"column"}
      width={"404px"}
      border={"1px solid black"}
      borderRadius={"5px"}
      height={"164px"}
      padding={"15px 17px"}
      justifyContent={"space-between"}
      sx={{
        background: "white",
      }}
    >
      <div
        ref={ref}
        style={{
          color: "#00182F",
          fontStyle: "normal",
          fontFamily: "Inter",
          fontSize: `${fontSize}px`,
          fontWeight: "400",
          padding: "0 !important",
          fontFeatureSettings: "'liga' off, 'clig' off",
        }}
      >
        {msg_content}
      </div>

      <Grid
        container
        height={"fit-content"}
        margin={"0 !important"}
        justifyContent={"space-between"}
        alignItems={"center"}
      >
        <Typography
          sx={{
            color: "#00182F",
            fontStyle: "normal",
            fontFamily: "Inter",
            fontSize: "12px",
            fontWeight: "400",
            padding: "0 !important",
            fontFeatureSettings: "'liga' off, 'clig' off",
          }}
        >
          <DatePipe
            date={msg_date}
            offset={-4}
            formatString="dd/MM/yyyy - hh:mm a"
          />
        </Typography>
        {!noActions && (
          <div style={{ display: "flex", flexDirection: "row", gap: "7px" }}>
            <Button
              sx={{
                background: "#FFFF",
                border: "1px solid rgba(221, 27, 27, 1)",
                borderRadius: "8px",
                width: "89px",
                height: "36px",
                color: "rgba(221, 27, 27, 1)",
                fontStyle: "normal",
                fontFamily: "Inter",
                fontSize: "15px",
                fontWeight: "500",
                padding: "0 !important",
                fontFeatureSettings: "'liga' off, 'clig' off",
                textTransform: "none",
              }}
              onClick={() => handleRejectMessage(msg_content, msg_date, msg_id)}
            >
              Rechazar
            </Button>
            <Button
              sx={{
                background: "#3DB76E",
                borderRadius: "8px",
                width: "127px",
                height: "36px",
                color: "#FFFF",
                fontStyle: "normal",
                fontFamily: "Inter",
                fontSize: "15px",
                fontWeight: "500",
                padding: "0 !important",
                fontFeatureSettings: "'liga' off, 'clig' off",
                textTransform: "none",
                "&:hover": {
                  background: "#3DB75E",
                },
              }}
              onClick={() =>
                handleApproveMessage(msg_content, msg_date, msg_id)
              }
            >
              Aprobar SMS
            </Button>
          </div>
        )}
      </Grid>
    </Grid>
  )
}

MessageCard.propTypes = {
  msg_id: PropTypes.number.isRequired,
  msg_date: PropTypes.string.isRequired,
  msg_content: PropTypes.string.isRequired,
  handleApproveMessage: PropTypes.func.isRequired,
  handleRejectMessage: PropTypes.func.isRequired,
  noActions: PropTypes.bool,
}

export default MessageCard
