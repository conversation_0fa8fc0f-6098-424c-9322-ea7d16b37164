import React from "react"
import PropTypes from "prop-types"
import { useApp } from "../../AppProvider"
import { useNavigate } from "react-router"
import {
  Button,
  Grid,
  Typography,
  IconButton,
  TextField,
  Tooltip,
  Box,
  DialogActions,
} from "@mui/material"
import { makeStyles } from "@mui/styles"
import MenuItem from "@mui/material/MenuItem"
import { useState, useCallback, useEffect } from "react"
import CalendarTodayIcon from "@mui/icons-material/CalendarToday"
import { InputAdornment } from "@mui/material"
import { useForm, Controller, get } from "react-hook-form"
import AttachFileIcon from "@mui/icons-material/AttachFile"
import CircularProgress from "@mui/material/CircularProgress"
import * as moment from "moment"
import "moment/locale/es"
import SecundaryButton from "../../components/SecundaryButton/SecundaryButton"
import PrimaryButton from "../../components/PrimaryButton/PrimaryButton"
import { MobileDatePicker } from "@mui/x-date-pickers/MobileDatePicker"
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment"
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider"
import { getTrivia , getTriviaReport} from "./services/api"
import * as XLSX from 'xlsx';

import { ThemeProvider, createTheme } from "@mui/material/styles"
import { set } from "date-fns"
moment.locale("es")

const useStyles = makeStyles(() => ({
  baseActionButton: {
    fontSize: "14px !important",
    width: "130px",
    fontfamily: "Ubuntu !important",
    fontWeight: "600 !important",
    lineHeight: "28px !important",
    backgroundColor: "white !important",
    padding: "5px  !important",
    border: "none",
    boxShadow:
      "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
    borderRadius: "8px !important",
    color: "#D9D9D9 !important",
    textTransform: "none !important",
    "@media screen and (maxWidth: 400px)": {
      fontSize: "0.4rem",
    },
    fontFamily: "Ubuntu !important",
  },
  nextActionButton: {
    backgroundColor: "#4E5589 !important",
    color: "white !important",
  },
  nextActionButtonDisabled: {
    backgroundColor: "#CFD8E0 !important",
  },
  baseInputStyle: {
    backgroundColor: "white",
  },
  baseInputLabel: {
    backgroundColor: "white",
    "& .MuiFormHelperText-contained": {
      backgroundColor: "#F5F4F4",
      margin: 0,
      paddingLeft: "2px",
      paddingTop: "5px",
    },
    "& .MuiInputLabel-root": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiInputLabel-shrink": {
      color: "#000000",
    },
    "& .MuiButtonBase-root": {
      color: "black",
    },
  },
  select: {
    backgroundColor: "white",
    "&:after": {
      borderBottomColor: "darkred",
    },
    "& .MuiInputLabel-root": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiSvgIcon-root": {
      color: "#009EFF",
      fontSize: "3.2rem",
      height: "56px",
      borderLeft: "1px solid #C4C4C4",
      borderRadius: "1",
      right: "0px",
      top: "0px",
      width: "45px",
      borderColor: "transparent",
    },
    "& .MuiFormHelperText-contained": {
      margin: 0,
      paddingLeft: "10px",
      paddingTop: "5px",
    },
    "&:click ": {
      color: "red",
    },
  },
  fileUpload: {
    backgroundColor: "white",
    "& .MuiFormHelperText-contained": {
      margin: 0,
      paddingLeft: "10px",
      paddingTop: "5px",
    },
    "&:after": {
      borderBottomColor: "darkred",
    },
    "& .MuiInputLabel-root": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiInputBase-root": {
      padding: "0",
    },
    "& .MuiButtonBase-root": {
      padding: "11px",
    },
    "&:click ": {
      color: "red",
    },
  },
  fileHasErrors: {
    color: "#FF4000",
  },
  fileHasNoErrors: {
    color: "#009EFF",
  },
  statisticsContainer: {
    marginTop: "20px",
    width: "97%",
    marginLeft: "3%",
    border: "1px solid #C4C4C4",
    borderRadius: "4px",
    backgroundColor: "#E3E8EE",
    padding: "10px 20px 10px 20px",
    fontFamily: "Ubuntu",
  },
  statisticsBoldText: {
    fontWeight: "bold",
    fontSize: "11px",
  },
  statisticsNormalText: {
    fontWeight: "normal",
    fontSize: "12px",
    marginLeft: "15px",
  },
}))

function CircularProgressWithLabel(props) {
  return (
    <Box sx={{ position: "relative", display: "inline-flex" }}>
      <CircularProgress variant="determinate" {...props} value={props.value} />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: "absolute",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography
          variant="caption"
          component="div"
          sx={{ color: "#000000", fontSize: "10px" }}
        >{`${Math.round(props.value)}%`}</Typography>
      </Box>
    </Box>
  )
}

CircularProgressWithLabel.propTypes = {
  value: PropTypes.number.isRequired,
}

const MyActionBar = ({ onAccept, onCancel }) => {
  return (
    <DialogActions>
      <Button onClick={onCancel}> CANCELAR </Button>
      <Button onClick={onAccept}> OK </Button>
    </DialogActions>
  )
}

MyActionBar.propTypes = {
  onAccept: PropTypes.any,
  onCancel: PropTypes.any,
}

const exportToExcel = (groupedUsers, type, triviaName) => {
  const data = [];

  Object.entries(groupedUsers).forEach(([day, users]) => {
    let isFirstEntryForDay = true;
    users.forEach((user) => {
      const row = {
        dia: day,
        nombre: user.user_name || '',
        telefono: user.user_phone || '',
        email: user.user_email || '',
        alias: user.user_alias || '',
      };

      if (type !== 'usuarios') {
        row.puntos = user.total_points || '';
      }

      data.push(row);
      isFirstEntryForDay = false;
    });
  });

  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Usuarios Agrupados');

  const currentDate = moment().format('YYYY-MM-DD');
  const fileName = `reporte_${type}_${triviaName}_${currentDate}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

const TriviaReport = () => {

  const { showNotification } = useApp()

    const [newDate, setNewDate] = useState(null)

  const [openDatePicker, setOpenDatePicker] = useState(false)
  const [trivias, setTrivias] = useState([]); // Inicializar como um array vazio


  const navigate = useNavigate()
  const classes = useStyles()

  const theme = createTheme({
    components: {
      MuiPickersToolbar: {
        styleOverrides: {
          penIconButton: {
            pointerEvents: "none",
            visibility: "hidden",
          },
        },
      },
      MuiTimePickerToolbar: {
        styleOverrides: {
          ampmSelection: {
            pointerEvents:
              newDate &&
              moment(newDate).isSame(moment(), "day") &&
              moment().isSameOrAfter(moment().hours(12), "hours")
                ? "none"
                : "",
          },
        },
      },
    },
  })

  const {
    handleSubmit,
    setValue,
    reset,
    setError,
    watch,
    trigger,
    control,
    getValues,
    formState: { isValid, dirtyFields, errors },
  } = useForm({
    mode: "all",
    defaultValues: {
      product: "",
      type: "",
      triviaId:'',
      startDate: "",
      endDate: "",
     
    },
  })

  const {
    product,
    type,
    startDate,
    endDate,
    triviaId
    
  } = watch()

  

  const fetchBusiness = async () => {
    const trivias = await getTrivia();
    setTrivias(trivias.data);
    console.log("Trivias fetched:", trivias.data);
  };

  useEffect(() => {
    fetchBusiness()
  }, [])

  const onSubmit = async (data) => {
    const parsedData = {
      ...data,
      startDate: data.startDate ? moment(data.startDate).format('YYYY-MM-DD') : null,
      endDate: data.endDate ? moment(data.endDate).format('YYYY-MM-DD') : null,
    };
    console.log('Formulario enviado:', parsedData);

    const report = await getTriviaReport(parsedData);

    // Agrupar usuarios por día
    const groupedUsers = report.data.users.reduce((acc, user) => {
      const day = user.day;
      if (!acc[day]) {
        acc[day] = [];
      }
      acc[day].push(user);
      return acc;
    }, {});

  

    // Exportar a Excel
    exportToExcel(groupedUsers, type, trivias.find((t) => t.id === triviaId)?.title || '');
  }



  return (
    <Grid container direction="row" wrap="nowrap" paddingBottom={"30px"}>
     {/*  */}
      <Grid item container direction="column" xs={12} sm={12} md={12}>
        <Grid item>
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Inter !important",
              fontStyle: "normal !important",
              fontWeight: "700 !important",
              fontSize: "25px !important",
              lineHeight: "24px !important",
              letterSpacing: "0.15px !important",
              color: "#363636 !important", 
              marginTop: "9px",
              marginBottom: "10px",
            }}
          >
           Generar Reporte
          </Typography>
        
        </Grid>
        <Grid item container direction="column" sx={{ marginTop: "30px" }}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            style={{ width: "30%", margin: "0 auto" }}
          >
            <Grid container direction="column" spacing={2}>
              <Grid
                item
                container
                direction="row"
                alignItems="center"
                justifyContent="center"
              >
                <Grid item xs={12} sm={12} md={12}>
                  <Controller
                    name="product"
                    control={control}
                    rules={{ required: "Campo obligatorio" }}
                    render={({ field, fieldState: { error }, formState }) => (
                      <TextField
                        id="product-input"
                        select
                        fullWidth
                        label="Producto"
                        helperText={error ? error.message : null}
                        error={!!error}
                        className={classes.select}
                        {...field}
                      >
                       
                          <MenuItem
                            sx={{ maxWidth: "600px" }}
                            key={'tribu'}
                            value={'tribu'}
                          >
                            Tribudeportiva
                          </MenuItem>

                         {/*  <MenuItem
                            sx={{ maxWidth: "600px" }}
                            key={'aqustico'}
                            value={'aqustico'}
                          >
                            Aqustico
                          </MenuItem> */}
                        
                      </TextField>
                    )}
                  />
                </Grid>
              </Grid>
             <Grid
                item
                container
                direction="row"
                alignItems="center"
                justifyContent="center"
              >
                <Grid item xs={12} sm={12} md={12}>
                  <Controller
                    name="triviaId"
                    control={control}
                    rules={{ required: "Campo obligatorio" }}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        id="trivia-select"
                        select
                        disabled={trivias.length === 0}
                        fullWidth
                        label="Trivia"
                        helperText={error ? error.message : null}
                        error={!!error}
                        className={classes.select}
                        {...field}
                      >

                        {trivias.map((trivia) => (
                            <MenuItem
                              sx={{ maxWidth: "600px" }}
                              key={trivia.id}
                              value={trivia.id}
                            >
                              {trivia.title}
                            </MenuItem>
                          ))}
                      </TextField>
                    )}
                  />
                </Grid>
              </Grid>
        
              <Grid
                item
                container
                direction="row"
                alignItems="center"
                justifyContent="center"
              >
                <Grid item xs={12} sm={12} md={12}>
                    <Controller
                    name="type"
                    control={control}
                    rules={{ required: "Campo obligatorio" }}
                    render={({ field, fieldState: { error }, formState }) => (
                      <TextField
                        id="product-input"
                        select
                        fullWidth
                        label="Tipo de reporte"
                        helperText={error ? error.message : null}
                        error={!!error}
                        className={classes.select}
                        {...field}
                      >
                       
                          <MenuItem
                            sx={{ maxWidth: "600px" }}
                            key={'usuarios'}
                            value={'usuarios'}
                          >
                            Jugadores
                          </MenuItem>

                          <MenuItem
                            sx={{ maxWidth: "600px" }} 
                            key={'winners'}
                            value={'ganadores'}
                          >
                            Ganadores
                          </MenuItem>
                        
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid
                item
                container
                direction="row"
                justifyContent="space-between"
                style={{ marginTop: "20px", width: "100%", gap: "10px" }}
              >
                <ThemeProvider theme={theme}>
                  <LocalizationProvider dateAdapter={AdapterMoment}>
                    <Grid item sx={{ width: "48%" }}>
                      <Controller
                        name="startDate"
                        control={control}
                        rules={{ required: "Campo obligatorio" }}
                        render={({ field, fieldState: { error } }) => (
                          <MobileDatePicker
                            label="Desde"
                            value={field.value}
                            inputFormat="DD/MM/YYYY"
                            onChange={(newValue) => {
                              setValue("startDate", newValue, {
                                shouldDirty: true,
                                shouldValidate: true,
                              });
                              trigger("startDate");
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                sx={{ width: "100%" }}
                                helperText={error ? error.message : null}
                                error={!!error}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <CalendarTodayIcon sx={{ fontSize: "25px", color: "black" }} />
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        )}
                      />
                    </Grid>
                    <Grid item sx={{ width: "48%" }}>
                      <Controller
                        name="endDate"
                        control={control}
                        rules={{ required: "Campo obligatorio" }}
                        render={({ field, fieldState: { error } }) => (
                          <MobileDatePicker
                            label="Hasta"
                            value={field.value}
                            inputFormat="DD/MM/YYYY"
                            disableFuture
                            minDate={watch("startDate") || null} // Desactiva días anteriores al campo Desde
                            onChange={(newValue) => {
                              setValue("endDate", newValue, {
                                shouldDirty: true,
                                shouldValidate: true,
                              });
                              trigger("endDate");
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                sx={{ width: "100%" }}
                                helperText={error ? error.message : null}
                                error={!!error}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <CalendarTodayIcon sx={{ fontSize: "25px", color: "black" }} />
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        )}
                      />
                    </Grid>
                  </LocalizationProvider>
                </ThemeProvider>
              </Grid>
              </Grid>
        
             
        
              <Grid
                item
                container
                direction="row"
                justifyContent="center"
                sx={{ marginTop: "36px", overflowY: "hidden", gap: "20px" }}
              >
                <Grid item>
                  <SecundaryButton
                    text={'Cancelar'}
                    action={() => reset()} // Resetea los datos del formulario
                    width="125px"
                    height="40px"
                    fontSize="14"
                    lineHeight="28"
                    type="button"
                    noShadow={true}
                  />
                </Grid>
                <Grid item>

                    <PrimaryButton
                    text={'Generar'}
                    action={() => {}}
                    width="125px"
                    height="40px"
                    fontSize="14"
                    lineHeight="28"
                    type="submit"
                    noShadow={true}
                    />
        
                  
                </Grid>
              </Grid>
            </Grid>
          </form>
        </Grid>
      </Grid>

    </Grid>
  )
}

export default TriviaReport
