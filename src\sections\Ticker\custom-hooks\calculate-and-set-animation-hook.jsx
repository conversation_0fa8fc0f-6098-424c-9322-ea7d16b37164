import { useState, useCallback } from "react"

export const useCalculateAndSetAnimation = (
  tickerRef,
  containerRef,
  containerWrapRef,
  isShutdown,
  ANIMATION_SPEED_PPS,
  animation
) => {

  const calculateAndSetAnimation = useCallback(
    () => {
      if (isShutdown) return
      if (tickerRef.current && tickerRef.current.children.length > 0) {
        console.log(containerWrapRef.current.offsetWidth)
        animation.effect.updateTiming({
          duration: containerWrapRef.current.offsetWidth * (1400 / ANIMATION_SPEED_PPS),
        })
      }
    },
    [
      ANIMATION_SPEED_PPS,
      isShutdown,
      tickerRef,
      containerRef,
      containerWrapRef
    ]
  )


  return { calculateAndSetAnimation, containerWrapRef }
}
