import { useState, useCallback } from "react"

export const useCalculateAndSetAnimation = (
  tickerRef,
  containerRef,
  containerWrapRef,
  isShutdown,
  ANIMATION_SPEED_PPS
) => {

  const calculateAndSetAnimation = useCallback(
    (animation) => {
      if (isShutdown) return
      if (tickerRef.current && tickerRef.current.children.length > 0 && animation) {
        animation.effect.updateTiming({
          duration: containerWrapRef.current.getBoundingClientRect().width * (1400 / ANIMATION_SPEED_PPS),
        })
      }
    },
    [
      ANIMATION_SPEED_PPS,
      isShutdown,
      tickerRef,
      containerRef,
      containerWrapRef
    ]
  )


  return { calculateAndSetAnimation, containerWrapRef }
}
