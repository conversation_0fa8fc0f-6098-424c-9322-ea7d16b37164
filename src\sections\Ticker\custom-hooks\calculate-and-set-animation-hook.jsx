import { useState, useCallback } from "react"

export const useCalculateAndSetAnimation = (
  tickerRef,
  containerRef,
  setAnimationStyle,
  isShutdown,
  ANIMATION_SPEED_PPS,
  separatorWidht
) => {
  const [delayPrecisionError, setDelayPrecisionError] = useState(0)

  const calculateAndSetAnimation = useCallback(
    (startPoint, element, restElements, keepDelay, receivedAfter, offset) => {
      if (isShutdown) return
      if (tickerRef.current && tickerRef.current.children.length > 0) {
        let totalWidth = 0
        let widthInitialMessage = 0
        const childrenCount = tickerRef.current.children.length
        const containerWidth =
          containerRef.current.getBoundingClientRect().width

        for (let i = 0; i < childrenCount; i++) {
          totalWidth +=
            tickerRef.current.children[i].getBoundingClientRect().width
          if (i < element - 1 && restElements) {
            widthInitialMessage +=
              tickerRef.current.children[i].getBoundingClientRect().width

            if (i > 2 && i < childrenCount - 1) {
              let overspace = Math.trunc(
                tickerRef.current.children[i].getBoundingClientRect().left -
                  tickerRef.current.children[i - 1].getBoundingClientRect()
                    .right
              )
              if (overspace > 5 || overspace < -5) {
                widthInitialMessage += overspace
              }
            }

          }
          if (receivedAfter && i == element - 1) {
            widthInitialMessage +=
              tickerRef.current.children[i].getBoundingClientRect().width -
              (separatorWidht + 100) +
              (offset || 0)
          }
        }
        const distance = containerWidth + totalWidth
        const duration = distance / ANIMATION_SPEED_PPS
        let animationDelay = "0"
        if (restElements || receivedAfter) {
          let delay = 0
          if (element == childrenCount - 1 && delayPrecisionError > 0.001) {
            delay =
              widthInitialMessage / ANIMATION_SPEED_PPS + delayPrecisionError
            setDelayPrecisionError(0)
          } else delay = widthInitialMessage / ANIMATION_SPEED_PPS
          const { truncated, remainder } = splitAtDecimal(delay, 3)
          setDelayPrecisionError((prev) => {
            return prev + remainder
          })
          animationDelay = `-${truncated}s`
        }
        setAnimationStyle((prev) => {
          if (prev[element]) {
            prev[element] = {
              "--ticker-start-x": `${startPoint}px`,
              "--ticker-end-x": `-${totalWidth}px`,
              animationName: "ticker-animation",
              animationDuration: `${Math.max(duration, 1)}s`,
              animationTimingFunction: "linear",
              animationFillMode: "forwards",
              animationDelay:
                restElements || receivedAfter
                  ? animationDelay
                  : keepDelay && prev[element].animationDelay
                  ? prev[element].animationDelay
                  : "0s",
            }
          } else {
            prev.push({
              "--ticker-start-x": `${startPoint}px`,
              "--ticker-end-x": `-${totalWidth}px`,
              animationName: "ticker-animation",
              animationDuration: `${Math.max(duration, 1)}s`,
              animationTimingFunction: "linear",
              animationFillMode: "forwards",
              animationDelay:
                restElements || receivedAfter ? animationDelay : "0s",
            })
          }
          return [...prev]
        })
      } else {
        setAnimationStyle([{ animationName: "none" }])
      }
    },
    [
      ANIMATION_SPEED_PPS,
      isShutdown,
      tickerRef,
      containerRef,
      setAnimationStyle,
      separatorWidht,
      delayPrecisionError,
    ]
  )

  function splitAtDecimal(number, decimalPlaces) {
    // 1. Calculate the multiplier (e.g., 10^3 = 1000)
    const factor = 10 ** decimalPlaces

    // 2. Shift decimal, truncate, and shift back
    const truncatedPart = Math.trunc(number * factor) / factor

    // 3. Find the remainder by subtracting
    const remainderPart = number - truncatedPart

    return {
      truncated: truncatedPart,
      remainder: remainderPart,
    }
  }

  return { calculateAndSetAnimation }
}
