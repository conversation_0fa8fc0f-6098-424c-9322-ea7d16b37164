import { createSvgIcon, Grid, Typography } from "@mui/material"
import PropTypes from "prop-types"
import Icon from "../../../../assets/exclamation.png"
import { makeStyles } from "@mui/styles"
import React, { useEffect, useState } from "react"
import { tickerModels } from "../../../Ticker/enums/ticker-models"
import { updateParameters } from "../../services/api"
import { ArrowDropDown } from "@mui/icons-material"
import CompoundInputNumber from "../../../../components/CompoundInputNumber"
import FileInputManagment from "../fileInputManagment/FileInputManagment"
import PrimaryButton from "../../../../components/PrimaryButton"

const AddIcon = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="40"
    height="40"
    viewBox="0 0 45 45"
    fill="none"
  >
    <path
      d="M11.75 19.25H14.25V14.25H19.25V11.75H14.25V6.75H11.75V11.75H6.75V14.25H11.75V19.25ZM13 25.5C11.2708 25.5 9.64583 25.1719 8.125 24.5156C6.60417 23.8594 5.28125 22.9688 4.15625 21.8438C3.03125 20.7188 2.14063 19.3958 1.48438 17.875C0.828125 16.3542 0.5 14.7292 0.5 13C0.5 11.2708 0.828125 9.64583 1.48438 8.125C2.14063 6.60417 3.03125 5.28125 4.15625 4.15625C5.28125 3.03125 6.60417 2.14063 8.125 1.48438C9.64583 0.828125 11.2708 0.5 13 0.5C14.7292 0.5 16.3542 0.828125 17.875 1.48438C19.3958 2.14063 20.7188 3.03125 21.8438 4.15625C22.9688 5.28125 23.8594 6.60417 24.5156 8.125C25.1719 9.64583 25.5 11.2708 25.5 13C25.5 14.7292 25.1719 16.3542 24.5156 17.875C23.8594 19.3958 22.9688 20.7188 21.8438 21.8438C20.7188 22.9688 19.3958 23.8594 17.875 24.5156C16.3542 25.1719 14.7292 25.5 13 25.5Z"
      fill="#009EFF"
    />
  </svg>,
  "add_icon"
)

const useStyles = makeStyles(() => ({
  publicityInput: {
    width: "379px",
    height: "120px",
    padding: "19px 11px",
    fontFamily: "Inter",
    fontSize: "16px",
    color: "rgba(0, 0, 0, 0.87)",
    fontWeight: "400",
    minHeight: "40px",
    borderRadius: "10px",
    border: "1px solid #000000",
    outline: "none",
    // Hide default number input arrows
    "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
      appearance: "none",
      margin: 0,
    },
    "&[type=number]": {
      MozAppearance: "textfield",
    },
  },
  labelInput: {
    fontWeight: "600",
    fontFamily: "Inter",
    fontSize: "15px",
    lineHeight: "24px",
    color: "#00182F",
  },
  chipGrid: {
    display: "flex",
    flexWrap: "wrap",
    gap: "3px",
    marginTop: "10px",
    maxWidth: "380px",
    maxHeight: "68px",
    overflow: "auto",
    // Custom scrollbar styles
    "&::-webkit-scrollbar": {
      width: "4px",
      height: "4px",
    },
    "&::-webkit-scrollbar-track": {
      background: "transparent",
    },
    "&::-webkit-scrollbar-thumb": {
      background: "#c1c1c1",
      borderRadius: "2px",
    },
    "&::-webkit-scrollbar-thumb:hover": {
      background: "#a8a8a8",
    },
    "&::-webkit-scrollbar-corner": {
      background: "transparent",
    },
    "&::-webkit-scrollbar-button": {
      display: "none",
    },
    // Firefox scrollbar
    scrollbarWidth: "thin",
    scrollbarColor: "#c1c1c1 transparent",
  },
  chip: {
    display: "flex",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: "16px",
    padding: "6px 8px 6px 12px",
    fontSize: "14px",
    fontFamily: "Inter",
    fontWeight: "400",
    color: "#333",
    maxWidth: "121px",
    minWidth: "121px",
    height: "32px",
    border: "1px solid #ddd",
  },
  chipText: {
    flex: 1,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    marginRight: "6px",
  },
  deleteButton: {
    backgroundColor: "transparent",
    border: "none",
    cursor: "pointer",
    padding: "2px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: "50%",
    width: "18px",
    height: "18px",
    fontSize: "12px",
    color: "#666",
    "&:hover": {
      backgroundColor: "#ddd",
      color: "#000",
    },
  },
}))

const PublicityField = ({
  publicity,
  setPublicity,
  newPublicity,
  setNewPublicity,
  deletedPublicity,
  setDeletedPublicity,
}) => {
  const classes = useStyles()

  const [publicityToAdd, setPublicityToAdd] = useState("")

  const handleDeletePublicity = (id) => {
    const isInNewPublicity = newPublicity.find(item => item.id === id)

    if (isInNewPublicity) {
      setNewPublicity(prev => prev.filter(item => item.id !== id))
    } else {
      const isInPublicity = publicity.find(item => item.id === id)

      if (isInPublicity) {
        setPublicity(prev => prev.filter(item => item.id !== id))
        setDeletedPublicity(prev => [...prev, id])
      }
    }
  }

  return (
    <Grid
      item
      container
      direction={"column"}
      width={"380px"}
      height={"265px"}
      overflow={"hidden"}
    >
      <Typography
        className={classes.labelInput}
        sx={{
          marginBottom: "6px",
        }}
      >
        Publicidad del cintillo:
      </Typography>
      <textarea
        name="publicity"
        id="publicity"
        className={classes.publicityInput}
        placeholder="Ingresa el texto para tu mensaje comodín (publicidad)"
        value={publicityToAdd}
        onChange={(event) => setPublicityToAdd(event.target.value)}
      ></textarea>
      <Grid
        container
        direction={"row"}
        wrap="nowrap"
        margin={"5px 0 0 0"}
        justifyContent={"flex-start"}
        alignItems={"center"}
      >
        <button
          onClick={() => {
            if (publicityToAdd == "") return
            setNewPublicity((prev) => {
              return [
              ...prev,
              { id: (prev.length) * -1, message: publicityToAdd },
            ]})
            setPublicityToAdd("")
          }}
          style={{
            backgroundColor: "transparent",
            border: "none",
            cursor: "pointer",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            height: "fit-content",
            width: "fit-content",
            gap: "13px",
          }}
        >
          <AddIcon sx={{ width: "30px", height: "30px" }} />
          <Typography
            sx={{
              fontSize: "16px",
              fontFamily: "Inter",
              fontWeight: "400",
              color: "black",
            }}
          >
            Agregar otra publicidad
          </Typography>
        </button>
      </Grid>

      {/* Chip Grid for displaying publicity */}
      {(publicity.length > 0 || newPublicity.length > 0) && (
        <div className={classes.chipGrid}>
          {publicity.map((item) => (
            <div key={`publicity-${item.id}`} className={classes.chip}>
              <span className={classes.chipText} title={item.message}>
                {item.message}
              </span>
              <button
                className={classes.deleteButton}
                onClick={() => handleDeletePublicity(item.id)}
                title="Delete publicity"
              >
                ×
              </button>
            </div>
          ))}

          {newPublicity.map((item) => (
            <div key={`new-publicity-${item.id}`} className={classes.chip}>
              <span className={classes.chipText} title={item.message}>
                {item.message}
              </span>
              <button
                className={classes.deleteButton}
                onClick={() => handleDeletePublicity(item.id)}
                title="Delete publicity"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
    </Grid>
  )
}

PublicityField.propTypes = {
  publicity: PropTypes.array.isRequired,
  setPublicity: PropTypes.func.isRequired,
  newPublicity: PropTypes.array.isRequired,
  setNewPublicity: PropTypes.func.isRequired,
  deletedPublicity: PropTypes.array.isRequired,
  setDeletedPublicity: PropTypes.func.isRequired,
}

export default PublicityField
