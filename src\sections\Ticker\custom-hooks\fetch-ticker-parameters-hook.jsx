import { useEffect, useState } from "react"
import { getAllParameters } from "../services/api"

export const useFetchTickerParameters = (requestParameters) => {
  const [tickerParameters, setTickerParameters] = useState({
    speed: null,
    separator: null,
    separatorHeight: null,
    separatorWidth: null,
    model: null,
    shutdown: true,
    maxRepetitions: null,
    closeHour: null,
    eventDurationMinutes: null,
    size: null,
  })
  const [parametersFetched, setParametersFetched] = useState(false)

  const fetchTickerParameters = async () => {
    try {
      const response = await getAllParameters()
      const parameters = response.data
      setTickerParameters(parameters)
      setParametersFetched(true)
    } catch (error) {
      console.error("Error fetching ticker parameters:", error)
    }
  }

  useEffect(async () => {
    await fetchTickerParameters()
  }, [requestParameters])

  return { tickerParameters, setTickerParameters, parametersFetched }
}
