import React from 'react';
import PropTypes from 'prop-types';
import corona from '../../../../assets/corona.png';

const WinnersList = ({ winners }) => {

  const columns = Math.ceil(winners.length / 5);

  return (
    <div
      id="winners-section"
      style={{
        display: "grid",
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: "20px",
        height:'65vh',
        width: winners.length <= 5 ? "40%" : "90%", // Ajustar ancho según cantidad de ganadores
        margin: "0 auto",
      
      }}
    >
      {Array.from({ length: columns }).map((_, colIndex) => (
        <div key={colIndex} style={{ display: "flex", flexDirection: "column" }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "20% 40% 40%",
              alignItems: "center",
              fontSize: "20px",
              fontWeight: "bold",
              color:"#FFF",
              backgroundColor: "transparent",
              borderRadius: "10px",
              padding: "10px",
              boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)" ,
            }}
          >
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
              <img
                src={corona}
                style={{ height: "8vh" }}
                alt="Corona"
              />
            </div>
            <div
              style={{
                textAlign: "center",
                fontFamily: "Montserrat",
                fontSize: winners.length === 5 ? "4vh" : "3.5vh",
                fontWeight: "700",
                
              }}
            >
              {"Alias"}
            </div>
            <div
              style={{
                textAlign: "center",
                fontFamily: "Montserrat",
                fontSize: winners.length === 5 ? "4vh" : "3.5vh",
                fontWeight: "700",
              
              }}
            >
              {"Puntaje"}
            </div>
          </div>
          {winners
            .slice(colIndex * 5, colIndex * 5 + 5)
            .map((winner, index) => (
              <div
                key={index}
                style={{
                  display: "grid",
                  gridTemplateColumns: "20% 40% 40%",
                  alignItems: "center",
                  backgroundColor: "#FFF",
                  borderRadius: "10px",
                  padding: "1.5vh",
                  boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                  fontSize: "4vh",
                  fontWeight: "bold",
                  color: "#000",
                  marginBottom: "20px", // Separación entre cada ganador
                }}
              >
                <div
                  style={{
                    textAlign: "center",
                    fontSize: "4vh",
                    fontWeight: "900",
                    position: "relative",
                  }}
                >
                  {colIndex * 5 + index + 1}
                  <div
                    style={{
                      position: "absolute",
                      top: "50%",
                      left: "100%",
                      width: "2px",
                      height: "40px",
                      backgroundColor: "#000",
                      transform: "translateY(-50%)",
                    }}
                  ></div>
                </div>
                <div
                  style={{
                    textAlign: "center",
                    fontSize: "3vh",
                    position: "relative",
                  }}
                >
                  {winner.id_user.alias}
                  <div
                    style={{
                      position: "absolute",
                      top: "50%",
                      left: "100%",
                      width: "2px",
                      height: "40px",
                      backgroundColor: "#000",
                      transform: "translateY(-50%)",
                    }}
                  ></div>
                </div>
                <div style={{ textAlign: "center", fontSize: "3vh" }}>
                  {winner.total_points} PTS
                </div>
              </div>
            ))}
        </div>
      ))}
    </div>
  );
};

WinnersList.propTypes = {
  winners: PropTypes.any.isRequired,
};

export default WinnersList;
