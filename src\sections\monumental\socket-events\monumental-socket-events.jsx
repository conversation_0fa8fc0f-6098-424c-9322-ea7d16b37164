
export const MonumentalMessagesSocketEvents = {
  MODERATOR_REQUEST_MESSAGES : 'M<PERSON><PERSON><PERSON>OR_REQUEST_MESSAGES',
  SEND_MESSAGES_TO_MODERATOR : 'SEND_MESSAGES_TO_MODERATOR',
  M<PERSON><PERSON><PERSON>OR_IS_FULL : 'M<PERSON><PERSON><PERSON>OR_IS_FULL',
  APROVED_MESSAGE : 'APROVED_MESSAGE',
  APROVED_MESSAGE_MULTIPLE : 'APROVED_MESSAGE_MULTIPLE',
  REJECTED_MESSAGE : 'REJECTED_MESSAGE',
  FINISH_CHANGING_STATUS : 'FINISH_CHANGING_STATUS',
  ERROR_CHANGING_STATUS : 'ERROR_CHANGING_STATUS',
  ERROR_FETCHING_MESSAGES : 'ERROR_FETCHING_MESSAGES',
  MESSAGE_NOW_NOT_NEW : 'MESSAGE_NOW_NOT_NEW',
  MESSAGE_NOW_NOT_NEW_MULTIPLE : 'MESSAGE_NOW_NOT_NEW_MULTIPLE',
  UNPUBLISH_MESSAGE : 'UNPUBLISH_MESSAGE',
  RE<PERSON>UEST_TOTAL_MESSAGE_QUANTITY: 'REQUEST_TOTAL_MESSAGE_QUANTITY',
  SEND_TOTAL_MESSAGE_QUANTITY: 'SEND_TOTAL_MESSAGE_QUANTITY',
  MODERATOR_REQUEST_MESSAGES_INAPPROPIATE: 'MODERATOR_REQUEST_MESSAGES_INAPPROPIATE',
  SEND_MESSAGES_TO_MODERATOR_INAPPROPIATE: 'SEND_MESSAGES_TO_MODERATOR_INAPPROPIATE',
  MODERATOR_IS_FULL_INAPPROPIATE: 'MODERATOR_IS_FULL_INAPPROPIATE',
}