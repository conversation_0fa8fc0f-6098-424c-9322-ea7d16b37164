
import { createSvgIcon } from "@mui/material"
import { makeStyles } from "@mui/styles"
import PropTypes from "prop-types"

const DownArrowIcon = createSvgIcon(
  <path
    d="M1 16.0125L1.8875 15.125L6 19.2375L10.1125 15.125L11 16.0125L6 21.0125L1 16.0125Z"
    fill="#1C1B1F"
  />,
  "down_arrow_icon"
)

const UpArrowIcon = createSvgIcon(
  <path
    d="M1 7.9875L1.8875 8.875L6 4.7625L10.1125 8.875L11 7.9875L6 2.9875L1 7.9875Z"
    fill="#1C1B1F"
  />,
  "up_arrow_icon"
)

const useStyles = makeStyles(() => ({
  parameterInput: {
    fontFamily: "Inter",
    fontSize: "15px",
    color: "#000000",
    fontWeight: "600",
    minHeight: "40px",
    borderRadius: "10px",
    textAlign: "center",
    border: "1px solid #000000",
    outline: "none",
    // Hide default number input arrows
    "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
      appearance: "none",
      margin: 0,
    },
    "&[type=number]": {
      MozAppearance: "textfield",
    },
  },
  numberInputContainer: {
    display: "flex",
    alignItems: "start",
    gap: "0px",
  },
  numberInput1: {
    width: "100px",
    textAlign: "end !important",
    borderTopRightRadius: "0px !important",
    borderBottomRightRadius: "0px !important",
    borderRight: "none !important",
  },
  numberInput2: {
    width: "101px",
    textAlign: "start !important",
    borderRadius: "0px !important",
    borderLeft: "none !important",
    borderRight: "none !important",
    display: "flex",
    padding: "1px 0 0 4px",
    alignItems: "center",
    justifyContent: "start",
  },
  arrowButtonContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "0px",
  },
  arrowButtonNumberInput1: {
    border: "1px solid #000000",
    borderTopRightRadius: "10px !important",
    borderLeft: "none !important",
    borderBottom: "none !important",
    backgroundColor: "white",
    maxHeight: "20px",
    maxWidth: "35px",
    overflow: "hidden",
  },
  arrowButtonNumberInput2: {
    border: "1px solid #000000",
    borderBottomRightRadius: "10px !important",
    borderLeft: "none !important",
    borderTop: "none !important",
    backgroundColor: "white",
    maxHeight: "20px",
    maxWidth: "35px",
    overflow: "hidden",
  },
  inputError: {
    color: "red",
    borderColor: "red !important",
  },
}))

const CompoundInputNumber = ({
  name,
  id,
  onChange,
  value,
  upArrowAction,
  downArrowAction,
  middleText,
  error
}) => {
  const classes = useStyles()

  return (
    <div className={classes.numberInputContainer}>
      <input
        type="number"
        name={name}
        id={id}
        className={classes.parameterInput + " " + classes.numberInput1 +
          (error ? " " + classes.inputError : "")}
        value={value}
        onChange={onChange}
      />
      <div
        id={`${id}-label`}
        className={classes.parameterInput + " " + classes.numberInput2 +
          (error ? " " + classes.inputError : "")}
      >
        {middleText}
      </div>
      <div className={classes.arrowButtonContainer}>
        <button
          className={classes.arrowButtonNumberInput1 +
            (error ? " " + classes.inputError : "")}
          onClick={upArrowAction}
        >
          <UpArrowIcon
            sx={{
              position: "relative",
              top: "4px",
              left: "2.5px",
            }}
          />
        </button>
        <button
          className={classes.arrowButtonNumberInput2 +
            (error ? " " + classes.inputError : "")}
          onClick={downArrowAction}
        >
          <DownArrowIcon
            sx={{
              position: "relative",
              bottom: "11px",
              left: "2.5px",
            }}
          />
        </button>
      </div>
    </div>
  )
}

CompoundInputNumber.propTypes = {
  name: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.number.isRequired,
  upArrowAction: PropTypes.func.isRequired,
  downArrowAction: PropTypes.func.isRequired,
  middleText: PropTypes.string.isRequired,
  error: PropTypes.bool.isRequired,
}

export default CompoundInputNumber
