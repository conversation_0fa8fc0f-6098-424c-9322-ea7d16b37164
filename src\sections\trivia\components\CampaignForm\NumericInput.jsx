import React from 'react';
import PropTypes from 'prop-types';
import TextField from '@mui/material/TextField';

const NumericInput = (props) => {
  const handleNumericChange = (e) => {
    const numericValue = e.target.value.replace(/[^0-9]/g, '');
   
  };

  return (
    <TextField
      {...props}
      value={props.value}
      onChange={handleNumericChange}
    />
  );
};

NumericInput.propTypes = {
  value: PropTypes.string.isRequired,

};

export default NumericInput;
