import { useEffect, useRef, useState } from "react"
import { get } from "../helpers/axios"
import { debounce } from "lodash"

export const usePagination = (
  path,
  page,
  perPage,
  filter,
  filterElement,
  order,
  orderElement,
  showNotification,
  dataTransformHandler,
  customQueryParams = ""
) => {
  const [data, setData] = useState([])
  const [totalData, setTotalData] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    let isMounted = true

    const fetchData = async () => {
      setLoading(true)
      setData([])
      try {
        const response = await get(
          `${path}?page=${page}&perPage=${perPage}&order=${order}&orderByElement=${orderElement}&filter=${filter}&filterElement=${filterElement}${customQueryParams}`
        )
        if (isMounted) {
          setData(dataTransformHandler(response.data.elements))
          setTotalData(response.data.totalElements)
          setLoading(false)
        }
      } catch (error) {
        if (isMounted) {
          showNotification("Ha ocurrido un error al obtener los datos", "error")
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, [filter, filterElement, order, orderElement, page, perPage])

  return { data, totalData, loading }
}
