import React, { useEffect, useState } from "react"
import TableComponent from "../../../../components/SectionTable/Table"
import { usePagination } from "../../../../custom-hooks/pagination-hook"
import { Grid } from "@mui/material"
import { useApp } from "../../../../AppProvider"
import { DatePipe } from "../../pipes/date-pipe"
import { useNavigate } from "react-router-dom"
import downloadIcon from "../../../../assets/download-icon.svg"
import { getAllMessages, getGeneralParameters } from "../../api/api"
import { StatusPipe } from "../../pipes/status-pipe"
import XLSX from "xlsx-js-style"
import ReportFilter from "../../../../components/statisticsFilter/ReportFilter"

const headCells = [
  {
    id: "messageDateLabel",
    label: "<PERSON>cha de envío",
    numeric: false,
    width: "30%",
  },
  {
    id: "messageLabel",
    label: "Mensaje SMS",
    numeric: false,
    width: "70%",
  },
]

const columOrderToShow = [
  {
    name: "messageDateLabel",
    length: false,
    link: false,
  },
  {
    name: "messageLabel",
    length: false,
    link: false,
    multiline: true,
  },
]

const AllMessages = () => {
  const [searchInput, setSearchInput] = useState("")
  const [order, setOrder] = useState("asc")
  const [orderBy, setOrderBy] = useState("messageDateLabel")
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(5)
  const { showNotification } = useApp()
  const navigator = useNavigate()
  const [openFilter, setOpenFilter] = useState(false)

  const handleTransformMessages = (data) => {
    return data.map((d) => {
      return {
        ...d,
        messageDateLabel: DatePipe({
          date: d.msg_date,
          offset: -4,
          formatString: "dd/MM/yyyy - hh:mm a",
        }),
        messageLabel: d.content,
      }
    })
  }

  const { data, totalData, loading } = usePagination(
    "/monumental-messages/getAll",
    page + 1,
    rowsPerPage,
    searchInput,
    "messageLabel",
    order,
    orderBy,
    showNotification,
    handleTransformMessages
  )

  const searchInputChange = (event) => {
    setSearchInput(event.target.value)
    setPage(0)
  }

  const onDeleteSearchBar = () => {
    setSearchInput("")
  }

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc"
    setOrder(isAsc ? "desc" : "asc")
    setOrderBy(property)
  }

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleOnClickRow = (element) => {
    navigator("messageDetail", {
      state: {
        message: element,
        window: 2,
      },
    })
  }
  const downloadAllMessagesTXT = async ({ initDate, limitDate }) => {
    const paramsResponse = await getGeneralParameters()
    const params = paramsResponse.data
    const response = await getAllMessages({
      page: 1,
      perPage: 0,
      order: "ASC",
      orderElement: "messageDateLabel",
      filter: "",
      filterElement: "messageLabel",
      fromDate: initDate.utc(),
      toDate: limitDate.utc(),
    })
    const data = response.data.elements
    let carga_headers = [
      [
        "Fecha de Envío",
        "Mensaje SMS",
        "Estado",
        "Moderador",
        "Fecha de solicitud",
        "Hora de aprobación/rechazo",
      ],
    ]

    data.forEach((d) => {
      carga_headers.push([
        DatePipe({
          date: d.msg_date,
          offset: -4,
          formatString: "dd/MM/yyyy HH:mm",
        }),
        d.content,
        StatusPipe({ status: d.status }),
        d.changed_status_by,
        d.changed_status_date
          ? DatePipe({
              date: d.changed_status_date,
              offset: -4,
              formatString: "dd/MM/yyyy",
            })
          : "n/a",
        d.changed_status_date
          ? DatePipe({
              date: d.changed_status_date,
              offset: -4,
              formatString: "HH:mm",
            })
          : "n/a",
      ])
    })
    const rows = []
    carga_headers.forEach((row) => rows.push(row.join(params.txtRowSeparator)))
    const blob = new Blob([rows.join("\n")], {
      type: "text/plain;charset=utf-8",
    })

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `mensajes_sms_${DatePipe({
      date: initDate,
      offset: -4,
      formatString: "yyyy-MM-dd",
    })}_a_${DatePipe({
      date: limitDate,
      offset: -4,
      formatString: "yyyy-MM-dd",
    })}.txt`;
    link.click();
    URL.revokeObjectURL(url);
    
  }
  const downloadAllMessagesExcel = async ({ initDate, limitDate }) => {
    let nBook = XLSX.utils.book_new()
    const response = await getAllMessages({
      page: 1,
      perPage: 0,
      order: "ASC",
      orderElement: "messageDateLabel",
      filter: "",
      filterElement: "messageLabel",
      fromDate: initDate.utc(),
      toDate: limitDate.utc(),
    })
    const data = response.data.elements
    if (data.length == 0) {
      showNotification("No hay datos en el rango seleccionado", "error")
      return
    }
    let carga_headers = [
      [
        "Fecha de Envío",
        "Mensaje SMS",
        "Estado",
        "Moderador",
        "Fecha de solicitud",
        "Hora de aprobación/rechazo",
      ],
    ]

    const ws = XLSX.utils.aoa_to_sheet(carga_headers, { cellStyles: true })
    let wscols = [
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
      { wch: 35 },
    ]

    ws["!cols"] = wscols

    ws["A1"].s = {
      alignment: { horizontal: "center" },
      font: { bold: true, name: "arial", sz: 10 },

      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } },
      },
    }
    ws["B1"].s = {
      alignment: { horizontal: "center" },
      font: { bold: true, name: "arial", sz: 10 },

      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } },
      },
    }
    ws["C1"].s = {
      alignment: { horizontal: "center" },
      font: { bold: true, name: "arial", sz: 10 },

      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } },
      },
    }
    ws["D1"].s = {
      alignment: { horizontal: "center" },
      font: { bold: true, name: "arial", sz: 10 },

      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } },
      },
    }
    ws["E1"].s = {
      alignment: { horizontal: "center" },
      font: { bold: true, name: "arial", sz: 10 },

      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } },
      },
    }
    ws["F1"].s = {
      alignment: { horizontal: "center" },
      font: { bold: true, name: "arial", sz: 10 },

      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } },
      },
    }

    XLSX.utils.sheet_add_aoa(
      ws,
      data.map(
        ({
          msg_date,
          changed_status_date,
          content,
          status,
          changed_status_by,
        }) => [
          DatePipe({
            date: msg_date,
            offset: -4,
            formatString: "dd/MM/yyyy HH:mm",
          }),
          content,
          StatusPipe({ status }),
          changed_status_by,
          changed_status_date
            ? DatePipe({
                date: changed_status_date,
                offset: -4,
                formatString: "dd/MM/yyyy",
              })
            : "",
          changed_status_date
            ? DatePipe({
                date: changed_status_date,
                offset: -4,
                formatString: "HH:mm",
              })
            : "",
        ]
      ),
      { origin: "A2", cellStyles: true }
    )

    XLSX.utils.book_append_sheet(nBook, ws, "Todos los mensajes")
    XLSX.writeFile(
      nBook,
      `mensajes_sms_${DatePipe({
        date: initDate,
        offset: -4,
        formatString: "yyyy-MM-dd",
      })}_a_${DatePipe({
        date: limitDate,
        offset: -4,
        formatString: "yyyy-MM-dd",
      })}.xlsx`,
      {
        type: "buffer",
        bookType: "xlsx",
        cellStyles: true,
      }
    )
  }

  return (
    <React.Fragment>
      <Grid height={"0px"} width={"1093px"}>
        {data.length > 0 && (
          <button
            style={{
              backgroundColor: "#009EFF",
              color: "white",
              height: "55px",
              width: "55px",
              borderRadius: "50px",
              border: "none",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              textAlign: "center",
              position: "relative",
              left: "1049px",
              top: "-15px",
            }}
            onClick={() => {
              setOpenFilter(true)
            }}
          >
            <img src={downloadIcon} alt="|" width="18px" />
          </button>
        )}
      </Grid>
      <Grid container margin={"62px 0 0 20px"} width={"1093px"}>
        <TableComponent
          showSearchInput={true}
          searchInputConfig={{
            searchInputValue: searchInput,
            onChangeSearchInput: searchInputChange,
            onClearSearchInput: onDeleteSearchBar,
            searchInputPlaceHolder: "Buscar",
          }}
          tableHeaderProps={{
            order: order,
            orderBy: orderBy,
            onRequestSort: handleRequestSort,
            headCells: headCells,
            showActionCell: false,
          }}
          onClickRow={handleOnClickRow}
          filteredData={data}
          totalData={totalData}
          backPagination={true}
          multiline={true}
          noFoundDataLabel={"Lo sentimos, no hay resultados para tu búsquedas."}
          isLoading={loading}
          orderOfColumnsToDisplay={columOrderToShow}
          showMenuColum={false}
          menuColumConfig={{
            onOpenMenu: () => {},
          }}
          paginationConfig={{
            onPageChange: handleChangePage,
            onRowsPerPageChange: handleChangeRowsPerPage,
            page: page,
            rowsPerPage: rowsPerPage,
            rowsPerPageLabel: "Registro por páginas:",
            rowsPerPageSequence: [5, 10, 15],
          }}
        />
      </Grid>
      <ReportFilter
        open={openFilter}
        setOpen={setOpenFilter}
        actions={[downloadAllMessagesExcel, downloadAllMessagesTXT]}
        options={[
          { label: "Excel", value: 0 },
          { label: "TXT", value: 1 },
        ]}
      />
    </React.Fragment>
  )
}

export default AllMessages
