import { Grid, Typography } from "@mui/material"
import { DatePipe } from "../../pipes/date-pipe"
import BackButton from "../../../../components/BackButton"
import { useLocation, useNavigate } from "react-router-dom"
import React, { useEffect } from "react"
import HeadSectionComponent from "../../../../components/SectionTable/HeadSection"
import { makeStyles } from "@mui/styles"
import { useDynamicFontSize } from "../../custom-hooks/dinamic-font-size"
import { StatusPipe } from "../../pipes/status-pipe"

export const HeaderStyles = makeStyles(() => ({
  container: {
    marginBottom: "25px !important",
    "@media screen and (max-width: 1350px)": {
      marginBottom: "25px !important",
    },
  },
}))

const DetailMessage = () => {
  const location = useLocation()
  const { message, window } = location.state || {}
  const navigate = useNavigate()
  const customHeaderClasses = HeaderStyles()
  const { ref, fontSize } = useDynamicFontSize(2, 12, 14)

  const handleBackButton = () => {
    navigate("/monumental?window=" + window)
  }

  useEffect(() => {
    if (!location.state) navigate("/monumental")
  }, [])

  return (
    <React.Fragment>
      {!location.state ? (
        <React.Fragment />
      ) : (
        <Grid
          container
          direction={"row"}
          margin={"33px 0 0 205px"}
          gap={"83px"}
          width={"fit-content"}
        >
          <Grid item>
            <BackButton action={handleBackButton} />
          </Grid>
          <Grid item>
            <HeadSectionComponent
              title="Ver detalle mensaje SMS"
              subTitle="Aquí podrás <b>revisar la información</b> precisa de tu mensaje"
              tooltipTitle="Detalle SMS"
              onAddButtonClick={() => {}}
              showAddButton={false}
              customStyle={customHeaderClasses}
            />

            <Grid
              item
              container
              direction={"row"}
              gap={"31px"}
              padding={"31.5px 0 0 0"}
              border={"1px solid #C4C4C4"}
              borderRadius={"4px"}
              width={"739px"}
              height={"372px"}
            >
              <Grid width={"258px"} justifyContent={"end"} padding={"0"}>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 12px 0"}
                  textAlign={"end"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "400",
                    }}
                  >
                    Estado:
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 14px 0"}
                  textAlign={"end"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "400",
                    }}
                  >
                    Fecha de solicitud:
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 17.18px 0"}
                  textAlign={"end"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "400",
                    }}
                  >
                    Hora de solicitud:
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 11.22px 0"}
                  textAlign={"end"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "400",
                    }}
                  >
                    Hora de aprobación:
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 17.78px 0"}
                  textAlign={"end"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "400",
                    }}
                  >
                    Aprobado por:
                  </Typography>
                </Grid>
                <Grid width={"100%"} maxHeight={"24px"} textAlign={"end"}>
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "400",
                    }}
                  >
                    Mensaje de la campaña:
                  </Typography>
                </Grid>
              </Grid>

              <Grid width={"400px"} justifyContent={"start"} padding={"0"}>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 12px 0"}
                  textAlign={"start"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    <StatusPipe status={message.status} />
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 14px 0"}
                  textAlign={"start"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    <DatePipe
                      date={message.msg_date}
                      offset={-4}
                      formatString="dd/MM/yyyy"
                    />
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 17.18px 0"}
                  textAlign={"start"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    <DatePipe
                      date={message.msg_date}
                      offset={-4}
                      formatString="hh:mm a"
                    />
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 11.22px 0"}
                  textAlign={"start"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    {message.changed_status_date ? (
                      <DatePipe
                        date={message.changed_status_date}
                        offset={-4}
                        formatString="hh:mm a"
                      />
                    ) : (
                      "N/A"
                    )}
                  </Typography>
                </Grid>
                <Grid
                  width={"100%"}
                  maxHeight={"24px"}
                  margin={"0 0 17.78px 0"}
                  textAlign={"start"}
                >
                  <Typography
                    sx={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: "14px",
                      fontWeight: "500",
                    }}
                  >
                    {message.changed_status_by
                      ? message.changed_status_by
                      : "N/A"}
                  </Typography>
                </Grid>
                <Grid width={"100%"} maxHeight={"24px"} textAlign={"start"}>
                  <div
                    ref={ref}
                    style={{
                      lineHeight: "24px",
                      fontFamily: "Inter",
                      fontSize: `${fontSize}px`,
                      fontWeight: "500",
                    }}
                  >
                    {message.content}
                  </div>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      )}
    </React.Fragment>
  )
}

export default DetailMessage
