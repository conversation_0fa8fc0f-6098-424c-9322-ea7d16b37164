import { Route } from "react-router"
import Users from "../sections/users"
import { ROLES } from "../sections/sidebar/constants"
import Monumental from "../sections/monumental"
import Home from "../sections/home"
import Preview from "../sections/preview-trivia/Preview"
import DetailMessage from "../sections/monumental/components/detailMessage"
import TickerMonumental from "../sections/Ticker/Ticker"
import Trivia from "../sections/trivia"
import NewCampaign from "../sections/trivia/components/NewCampaign"
import TriviaReport from "../sections/trivia-report"
import TickerParametersConfiguration from "../sections/tickerParametersConfiguration"
export const ROUTES = {
  [ROLES.SUPER]: (
    <>
      <Route path="/users" element={<Users />} exact />

      <Route path="/monumental" element={<Monumental />} exact />

      <Route path="/trivia" element={<Trivia />} exact />

      <Route path="/trivia/new" element={<NewCampaign />} exact />

       <Route path="/report" element={<TriviaReport />} exact />

      <Route path="/configuration" element={<TickerParametersConfiguration />} exact />

      <Route
        path="/monumental/messageDetail"
        element={<DetailMessage />}
        exact
      />
    </>
  ),
  [ROLES.TRIVIA_ADMIN]: (
    <>
    {/*   <Route path="/home" element={<Home />} exact /> */}

     {/*  <Route path="/preview/:id" element={<Preview />} exact /> */}

     {/*  <Route path="/users" element={<Users />} exact /> */}

      <Route path="/trivia" element={<Trivia />} exact />

      <Route path="/trivia/new" element={<NewCampaign />} exact />

      <Route path="/report" element={<TriviaReport />} exact />


     
    </>
  ),
  [ROLES.TRIVIA_MODERATOR]: (
    <>
      <Route path="/home" element={<Home />} exact />

     {/*  <Route path="/preview/:id" element={<Preview />} exact /> */}
     
      <Route path="/trivia" element={<Trivia />} exact />
     
    </>
  ),
  [ROLES.ADMIN]: (
    <>

      <Route path="/users" element={<Users />} exact />
      
      <Route path="/monumental" element={<Monumental />} exact />

      <Route path="/configuration" element={<TickerParametersConfiguration />} exact />

    </>
  ),
  [ROLES.MODERATOR]: (
    <>

      <Route path="/monumental" element={<Monumental />} exact />

      <Route
        path="/monumental/messageDetail"
        element={<DetailMessage />}
        exact
      />
    </>
  )
}
