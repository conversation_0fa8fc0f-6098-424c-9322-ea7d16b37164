import React from 'react'
import PropTypes from 'prop-types'
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Button, Grid, Typography, Box } from "@mui/material";
import { makeStyles } from '@mui/styles';
import { disableBusiness } from "../../services/api";

const useStyles = makeStyles(() => ({
  baseActionButton: {
    fontSize: "20px !important",
    fontWeight: "bold !important",
    lineHeight: "28px !important",
    backgroundColor: "white !important",
    padding: "9px 18px 10px 18px !important",
    border: "1px solid #737373 !important",
    boxShadow:
      "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
    borderRadius: "8px !important",
    color: "#737373 !important",
    textTransform: "none !important",
    "@media screen and (maxWidth: 400px)": {
      fontSize: "0.4rem",
    },
    fontFamily: "Ubuntu !important",
  },
  nextActionButton: {
    backgroundColor: "#008C0D !important",
    color: "white !important",
  },
  nextActionButtonDisabled: {
    backgroundColor: "#737373 !important",
  },
  contentBodyText:{
    width: "85% !important",
    textAlign: "center !important",
    fontFamily: "Ubuntu !important",
    fontWeight: "400",
    fontSize: "20px !important",
    letterSpacing: "0.15px !important",
    lineHeight: "24px !important",

  }
}));

const CampaignMessageModal = ({open,setOpen, modalConfig}) => {
    const classes = useStyles();
    const { modalTitle ,campaignTitle, modalType, actionType, hasErrorDB,  acceptButtonLabel, cancelButtonLabel , callback } = modalConfig
    return (
      <Dialog fullWidth PaperProps={{ sx: { minWidth: "35%" } }} open={open}>
        <DialogTitle>
          <Typography
            sx={{
              fontSize: "30px",
              fontFamily: "Ubuntu",
              fontWeight: "bold",
              lineHeight: "24px",
              letterSpacing: "0.15px",
              margin:'9px 0px !important'
            }}
          >
            {modalTitle}
          </Typography>
        </DialogTitle>
        <DialogContent
          sx={{
            border: "1px solid #979797",
            borderRight: "none",
            borderLeft: "none",
            display: "flex",
            flexDirection: "column",
            padding: "52px 50px 52px 50px !important",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          { modalType == 'create' && actionType == 'submit'  ?
              hasErrorDB ?
                <Typography
                component="div"
                className={classes.contentBodyText}
                >
                    La base de datos de la campaña {" "}
                    <Box component="span" sx={{ fontWeight: "bold" }}>
                      {campaignTitle} {" "}
                    </Box> 
                    tiene registros con formato incorrecto. ¿Estás seguro que deseas crearla?
                </Typography>
              :
                <Typography
                  component="div"
                  className={classes.contentBodyText}
                >
                  ¿Estás seguro que deseas crear la siguiente campaña: {" "}
                  <Box component="span" sx={{ fontWeight: "bold" }}>
                    {campaignTitle}
                  </Box>
                  ?
              </Typography>
            : modalType == 'edit' && actionType == 'submit' ?
              hasErrorDB ? 
                <Typography
                  component="div"
                  className={classes.contentBodyText}
                  >
                  La campaña que estas editando tiene una base de datos con registros incorrectos. ¿Estás seguro que deseas continuar?
                </Typography>
              :
                <Typography
                component="div"
                className={classes.contentBodyText}
                >
                  ¿Estás seguro que deseas editar la siguiente campaña: {" "}
                  <Box component="span" sx={{ fontWeight: "bold" }}>
                    {campaignTitle}
                  </Box>
                ?
              </Typography>
            : modalType == 'cancel' && actionType == 'submit' ?
              <Typography
                  component="div"
                  className={classes.contentBodyText}
                  >
                  ¿Estás seguro que deseas cancelar la siguiente campaña: {" "}
                  <Box component="span" sx={{ fontWeight: "bold" }}>
                    {campaignTitle}
                  </Box>
                  ?
                </Typography>
            : 
            <Typography
             component="div"
             className={classes.contentBodyText}
           >
            ¿Estás seguro que deseas salir? La información cargada hasta el momento no se guardará.
           </Typography>
            }


        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", alignItems: "center", padding:'21px 0px 16px 0px !important' }}>
          <Grid
            container
            direction="row"
            wrap="nowrap"
            justifyContent="center"
            spacing={3}
          >
            <Grid item xs={4} sm={4} md={4}>
              <Button
                fullWidth
                className={classes.baseActionButton}
                onClick={() => {
                  setOpen(false);
                }}
                variant="contained"
              >
                {cancelButtonLabel ? cancelButtonLabel : 'Cancelar'}
              </Button>
            </Grid>
            <Grid item xs={4} sm={4} md={4}>
              <Button
                fullWidth
                className={`${classes.baseActionButton} ${classes.nextActionButton}`}
                sx={{minWidth:"200px"}}
                variant="contained"
                onClick={callback}
              >
                {acceptButtonLabel ? acceptButtonLabel : "Aceptar"}
              </Button>
            </Grid>
          </Grid>
        </DialogActions>
      </Dialog>
    );
}

CampaignMessageModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  modalConfig:PropTypes.shape({
    modalTitle: PropTypes.string.isRequired,
    campaignTitle:PropTypes.string,
    modalType:PropTypes.oneOf(['create', 'edit', 'cancel']).isRequired,
    actionType: PropTypes.oneOf(['submit', 'back']).isRequired,
    hasErrorDB:PropTypes.bool,
    callback: PropTypes.func,
    acceptButtonLabel: PropTypes.string,
    cancelButtonLabel: PropTypes.string,
  }),
}

export default CampaignMessageModal;
