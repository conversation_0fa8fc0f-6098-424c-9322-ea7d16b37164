import React, { useState } from "react"
import { createSvgIcon, Grid, ListItemIcon } from "@mui/material"
import PropTypes from "prop-types"
import ToVerifyMessages from "../toVerifyMessages"
import { useDebouncedWindowSize } from "../../custom-hooks/debounced-window-size"
import verifySelected from "../../../../assets/eye_tracking_selected.svg"
import verifyUnselected from "../../../../assets/eye_tracking_unselected.svg"
import deleteSelected from "../../../../assets/delete_selected.svg"
import deleteUnselected from "../../../../assets/delete_unselected.svg"
import DiscardedMessages from "../discardedMessages/discardedMessages"

const InappropiateMessages = ({
  socket,
  on,
  emit,
  off,
  inappropiateQuantity,
}) => {
  const [actualOption, setActualOption] = useState(0)
  const showMoreMessagesBreakPoint = 1850

  const windowSize = useDebouncedWindowSize()
  return (
    <React.Fragment>
      <Grid
        container
        direction="row"
        width={`${
          windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
        }px`}
        justifyContent={"center"}
        height={"0px"}
        position={"relative"}
        top={"60px"}
      >
        <div
          style={{
            width: "167px",
            height: "44px",
            fontFamily: "Inter",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: "700",
            lineHeight: "14px",
            display: "flex",
            gap: "7px",
            justifyContent: "center",
            alignItems: "center",
            ...(actualOption == 0 && {
              color: "#000",
              boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.10)",
              borderBottom: "2px solid black",
            }),
            ...(actualOption != 0 && {
              color: "#A3A3A3",
              cursor: "pointer",
            }),
          }}
          onClick={() => {
            if (actualOption == 1) setActualOption(0)
          }}
        >
          <img
            src={actualOption == 0 ? verifySelected : verifyUnselected}
            alt=""
            width={"24px"}
            height={"24px"}
          />
          <p>Por verificación</p>
        </div>
        <div
          style={{
            width: "167px",
            height: "44px",
            fontFamily: "Inter",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: "700",
            lineHeight: "14px",
            display: "flex",
            gap: "7px",
            justifyContent: "center",
            alignItems: "center",
            ...(actualOption == 1 && {
              color: "#000",
              boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.10)",
              borderBottom: "2px solid black",
            }),
            ...(actualOption != 1 && {
              color: "#A3A3A3",
              cursor: "pointer",
            }),
          }}
          onClick={() => {
            if (actualOption == 0) setActualOption(1)
          }}
        >
          <img
            src={actualOption == 1 ? deleteSelected : deleteUnselected}
            alt=""
            width={"24px"}
            height={"24px"}
          />
          <p>Descartados</p>
        </div>
      </Grid>
      {actualOption == 0 ? (
        <ToVerifyMessages
          socket={socket}
          on={on}
          off={off}
          emit={emit}
          inappropiateQuantity={inappropiateQuantity}
        />
      ) : (
        <React.Fragment />
      )}
      {actualOption == 1 ? <DiscardedMessages /> : <React.Fragment />}
    </React.Fragment>
  )
}

InappropiateMessages.propTypes = {
  socket: PropTypes.object,
  on: PropTypes.func.isRequired,
  emit: PropTypes.func.isRequired,
  off: PropTypes.func.isRequired,
  inappropiateQuantity: PropTypes.number.isRequired,
}

export default InappropiateMessages
