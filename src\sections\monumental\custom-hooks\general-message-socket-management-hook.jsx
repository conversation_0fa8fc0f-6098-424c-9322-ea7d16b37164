import { useState, useEffect } from "react"
import moment from "moment"
import { debounce } from "lodash"
import { MonumentalMessagesSocketEvents } from "../socket-events/monumental-socket-events"
import { useApp } from "../../../AppProvider"

export const useGeneralMessageSocketManagement = (
  messages,
  setMessages,
  socket,
  emit,
  on,
  off,
  requestEvent,
  requestQuantity,
  receiveEvent,
  minorFetchUmbral,
  maxFull,
  fullEvent,
  paginationLast,
  page
) => {
  const { currentUser, showNotification } = useApp()

  const [readyToSend, setReadyToSend] = useState(false)

  const [acceptAllTimeout, setAcceptAllTimeout] = useState(false)

  const [readyToFetch, setReadytoFetch] = useState(true)

  const [alreadyFetchedMessages, setAlreadyFetchedMessages] = useState(false)

  const [timeoutsIds, setTimeoutsIds] = useState([])

  let isMounted = true

  useEffect(() => {
    if (!socket) return
    // Listen for messages

    const sendMessagesCallback = (data) => {
      handleReceivedMessages(data)
    }

    const finishStatusCallback = () => {
      triggerFetchMessages()
    }

    const errorChangingStatusCallback = (data) => {
      handleErrorChangingStatus(data)
    }

    const messageNotNewCallback = (data) => {
      deleteMessageFromList(data.msg_id)
    }

    const messageNotNewMultipleCallback = (data) => {
      deleteMultipleMessagesFromList(data)
    }

    on(receiveEvent, sendMessagesCallback)

    on(
      MonumentalMessagesSocketEvents.FINISH_CHANGING_STATUS,
      finishStatusCallback
    )

    on(
      MonumentalMessagesSocketEvents.ERROR_CHANGING_STATUS,
      errorChangingStatusCallback
    )

    on(
      MonumentalMessagesSocketEvents.MESSAGE_NOW_NOT_NEW,
      messageNotNewCallback
    )

    on(
      MonumentalMessagesSocketEvents.MESSAGE_NOW_NOT_NEW_MULTIPLE,
      messageNotNewMultipleCallback
    )

    setReadyToSend(true)
    return () => {
      off(receiveEvent, sendMessagesCallback)

      off(
        MonumentalMessagesSocketEvents.FINISH_CHANGING_STATUS,
        finishStatusCallback
      )

      off(
        MonumentalMessagesSocketEvents.ERROR_CHANGING_STATUS,
        errorChangingStatusCallback
      )

      off(
        MonumentalMessagesSocketEvents.MESSAGE_NOW_NOT_NEW,
        messageNotNewCallback
      )

      isMounted = false

      timeoutsIds.forEach((timeout) => {
        clearTimeout(timeout)
      })
    }
  }, [socket, on, off])

  useEffect(() => {
    console.log("messages", messages)
    if (!isMounted) return
    if (!readyToSend || !readyToFetch) return
    if (messages.length < minorFetchUmbral && !alreadyFetchedMessages) {
      const pagination =
        messages.length > 0
          ? {
              last_msg_id: messages[messages.length - 1].msg_id,
              last_msg_date: messages[messages.length - 1].msg_date,
            }
          : page && page > 1
          ? {
              last_msg_id: paginationLast.find((p) => p.page == page - 1)
                .message.msg_id,
              last_msg_date: paginationLast.find((p) => p.page == page - 1)
                .message.msg_date,
            }
          : {}
      emit(requestEvent, {
        quantity: requestQuantity,
        ...pagination,
      })
      setAlreadyFetchedMessages(true)
    }
  }, [messages])

  const handleReceivedMessages = (newMessages) => {
    setAlreadyFetchedMessages(false)
    if (newMessages.messages.length > 0) {
      setMessages((prev) => {
        const newMessagesList = [...prev]
        newMessages.messages.forEach((m)=> {
          if (!prev.some( el => {return el.msg_id == m.msg_id}))
            newMessagesList.push(m)
        })
        if (newMessagesList.length >= maxFull) {
          emit(fullEvent)
        }
        console.log(newMessagesList)
        return newMessagesList
      })
    }
  }

  const handleApproveMessage = (messageContent, messageDate, messageId) => {
    setReadytoFetch(false)
    emit(MonumentalMessagesSocketEvents.APROVED_MESSAGE, {
      messageContent: messageContent,
      messageDate: messageDate,
      messageId: messageId,
      userId: currentUser.id,
    })
    deleteMessageFromList(messageId)
  }

  const handleRejectMessage = (messageContent, messageDate, messageId) => {
    setReadytoFetch(false)
    emit(MonumentalMessagesSocketEvents.REJECTED_MESSAGE, {
      messageContent: messageContent,
      messageDate: messageDate,
      messageId: messageId,
      userId: currentUser.id,
    })
    deleteMessageFromList(messageId)
  }

  const handleAproveAllMessages = (quantity) => {
    if (messages.length == 0) return
    setReadytoFetch(false)
    setAcceptAllTimeout(true)
    const timeoutId = setTimeout(() => {
      setAcceptAllTimeout(false)
      setTimeoutsIds((prev) => {
        return prev.filter((id) => id != timeoutId)
      })
    }, 2000)
    setTimeoutsIds((prev) => prev.concat([timeoutId]))
    const messagesToApprove = messages.slice(0, quantity).map((m) => ({
      messageContent: m.msg_content,
      messageDate: m.msg_date,
      messageId: m.msg_id,
      userId: currentUser.id,
    }))
    emit(MonumentalMessagesSocketEvents.APROVED_MESSAGE_MULTIPLE, {
      messages: messagesToApprove,
    })
    deleteMessagesFromList(quantity)
    showNotification("Los mensajes se aprobaron exitosamente", "success")
  }

  const deleteMessageFromList = debounce((id) => {
    if (!isMounted) return
    console.log(id)
    setMessages((prevMessages) => {
      console.log(prevMessages.filter((m) => m.msg_id != id))
      return prevMessages.filter((m) => m.msg_id != id)
    })
  }, 300)

  const deleteMessagesFromList = debounce((quantity) => {
    if (!isMounted) return
    setMessages((prevMessages) => {
      return prevMessages.slice(quantity, prevMessages.length - 1)
    })
  }, 300)

  const deleteMultipleMessagesFromList = debounce((ids) => {
    if (!isMounted) return
    setMessages((prevMessages) => {
      let newMessages = prevMessages
      ids.forEach(id => {
        newMessages = newMessages.filter((m) => m.msg_id != id.msg_id)
      })
      return newMessages
    })
  }, 300)

  const triggerFetchMessages = debounce(() => {
    if (!isMounted) return
    setReadytoFetch(true)
    setMessages((prev) => [...prev])
  }, 500)

  const handleErrorChangingStatus = (data) => {
    setTimeout(() => {
      setMessages((prev) => {
        const newMessages = prev
        data.forEach((m)=> {
          if (!prev.some( el => {return el.msg_id == m.msg_id}))
            newMessages.push(m)
        })
        return newMessages.sort(
          (a, b) => moment(a.msg_date).valueOf() - moment(b.msg_date).valueOf()
        )
      })
    }, 350)
    showNotification(
      "Lo sentimos, se ha producido un error al aprobar el mensaje, se ha devuelto a la lista",
      "error"
    )
  }

  return {
    readyToSend,
    acceptAllTimeout,
    handleAproveAllMessages,
    handleApproveMessage,
    handleRejectMessage,
    setAlreadyFetchedMessages,
    alreadyFetchedMessages
  }
}
