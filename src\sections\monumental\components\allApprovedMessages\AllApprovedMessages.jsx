import React, { useEffect, useState } from "react"
import TableComponent from "../../../../components/SectionTable/Table"
import { usePagination } from "../../../../custom-hooks/pagination-hook"
import { Grid } from "@mui/material"
import { useApp } from "../../../../AppProvider"
import { DatePipe } from "../../pipes/date-pipe"
import { useNavigate } from "react-router-dom"
import moment from "moment"
import ShutdownTickerButton from "../shutdownTickerButton/ShutdownTickerButton"
import { ROLES } from "../../../sidebar/constants"

const headCells = [
  {
    id: "messageDateLabel",
    label: "Fecha de envío",
    numeric: false,
    width: "30%",
  },
  {
    id: "messageLabel",
    label: "Mensaje SMS",
    numeric: false,
    width: "70%",
  },
]

const columOrderToShow = [
  {
    name: "messageDateLabel",
    length: false,
    link: false,
  },
  {
    name: "messageLabel",
    length: false,
    link: false,
    multiline: true,
  },
]

const AllApprovedMessages = () => {
  const [searchInput, setSearchInput] = useState("")
  const [order, setOrder] = useState("asc")
  const [orderBy, setOrderBy] = useState("messageDateLabel")
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(5)
  const { showNotification, currentUser } = useApp()
  const navigator = useNavigate()
  const handleTransformMessages = (data) => {
    return data.map((d) => {
      return {
        ...d,
        messageDateLabel: DatePipe({
          date: d.msg_date,
          offset: -4,
          formatString: "dd/MM/yyyy - hh:mm a",
        }),
        messageLabel: d.content,
      }
    })
  }

  const { data, totalData, loading } = usePagination(
    "/monumental-messages/getAll/APPROVED",
    page + 1,
    rowsPerPage,
    searchInput,
    "messageLabel",
    order,
    orderBy,
    showNotification,
    handleTransformMessages,
    "&fromDate=" + moment().toISOString() + "&toDate=" + moment().toISOString()
  )

  const searchInputChange = (event) => {
    setSearchInput(event.target.value)
    setPage(0)
  }

  const onDeleteSearchBar = () => {
    setSearchInput("")
  }

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc"
    setOrder(isAsc ? "desc" : "asc")
    setOrderBy(property)
  }

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleOnClickRow = (element) => {
    navigator("messageDetail", {
      state: {
        message: element,
        window: 1,
      },
    })
  }

  return (
    <React.Fragment>

      {currentUser.role == ROLES.SUPER && (
        <Grid
          item
          display={"flex"}
          justifyContent={"flex-start"}
          sx={{
            width: "100%",
            alignItems: "center",
            display: "flex",
            justifyContent: "flex-end",
            paddingRight: "54px",
          }}
        >
          <ShutdownTickerButton />
        </Grid>
      )}
      <Grid
        container
        margin={
          currentUser.role == ROLES.SUPER ? "52px 0 0 20px" : "62px 0 0 20px"
        }
        width={"1093px"}
      >
        <TableComponent
          showSearchInput={true}
          searchInputConfig={{
            searchInputValue: searchInput,
            onChangeSearchInput: searchInputChange,
            onClearSearchInput: onDeleteSearchBar,
            searchInputPlaceHolder: "Buscar",
          }}
          tableHeaderProps={{
            order: order,
            orderBy: orderBy,
            onRequestSort: handleRequestSort,
            headCells: headCells,
            showActionCell: false,
          }}
          onClickRow={handleOnClickRow}
          filteredData={data}
          totalData={totalData}
          backPagination={true}
          multiline={true}
          noFoundDataLabel={"Lo sentimos, no hay resultados para tu búsquedas."}
          isLoading={loading}
          orderOfColumnsToDisplay={columOrderToShow}
          showMenuColum={false}
          menuColumConfig={{
            onOpenMenu: () => {},
          }}
          paginationConfig={{
            onPageChange: handleChangePage,
            onRowsPerPageChange: handleChangeRowsPerPage,
            page: page,
            rowsPerPage: rowsPerPage,
            rowsPerPageLabel: "Registro por páginas:",
            rowsPerPageSequence: [5, 10, 15],
          }}
        />
      </Grid>
    </React.Fragment>
  )
}

export default AllApprovedMessages
