import { makeStyles } from "@mui/styles";

const drawerWidth = 240;
export const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
    height: "100vh",
    overflowY: "scroll",
  },
  drawer: {
    [theme.breakpoints.up("sm")]: {
      width: drawerWidth,
      flexShrink: 0,
    },
  },
  appBar: {
    backgroundColor: "white",
    boxShadow: "0px 4px 4px rgba(0, 0, 0, 0.25)",
    "@media print": {
      display: "none",
    },
  },
  search: {
    width: "56%",
  },
  listContainer: {
    padding: "0 7%",
  },
  list: {
    width: "100%",
  },
  menuButton: {
    marginRight: theme.spacing(2),
    [theme.breakpoints.up("sm")]: {
      display: "none",
    },
  },
  // necessary for content to be below app bar
  toolbar: {
    ...theme.mixins.toolbar,
    display: "flex",
    flexDirection: "column",
    backgroundColor: "white",
    alignItems: "center",
    justifyContent: "center",
  },
  drawerPaper: {
    backgroundColor: "#1D2939",
    color: "white",
    width: drawerWidth,
  },
  content: {
    flexGrow: 1,
    padding: "1.5%",
    backgroundColor: "#E9ECEF",
    "@media print": {
      height: "100%",
      width: "100%",
      position: "fixed",
    },
  },
  listItem: {
    borderRadius: 3,
    "&$selected": {
      backgroundColor: "#00B297",
    },
    "&:hover": {
      backgroundColor: "#18222F",
    },
  },
  listIcon: {
    minWidth: 0,
  },
  listText: {
    marginLeft: 15,
  },
  activeItem: {
    backgroundColor: "#00B297!important",
  },
}));
