import React from "react"
import PropTypes from "prop-types"
import TextField from "@mui/material/TextField"
import Dialog from "@mui/material/Dialog"
import DialogActions from "@mui/material/DialogActions"
import DialogContent from "@mui/material/DialogContent"
import DialogTitle from "@mui/material/DialogTitle"
import { useForm, Controller } from "react-hook-form"
import { createUser, updateUsers, updateUserEmail } from "../../services/api"

import { useEffect, useState } from "react"
import Box from "@mui/material/Box"
import InputLabel from "@mui/material/InputLabel"
import MenuItem from "@mui/material/MenuItem"
import FormControl from "@mui/material/FormControl"
import Select from "@mui/material/Select"
import useMediaQuery from "@mui/material/useMediaQuery"
import useTheme from "@mui/material/styles/useTheme"
import { makeStyles } from "@mui/styles"
import FormHelperText from "@mui/material/FormHelperText"
import { useApp } from "../../../../AppProvider"
import { email_regex } from "../../../../helpers/regular_expressions"
import SecundaryButton from "../../../../components/SecundaryButton/SecundaryButton"
import PrimaryButton from "../../../../components/PrimaryButton/PrimaryButton"

const classUseStyles = makeStyles(() => ({
  baseActionButton: {
    fontSize: "14px !important",
    minWidth: "140px !important",
    fontWeight: "600 !important",
    lineHeight: "28px !important",
    backgroundColor: "#FFFFFF !important",
    padding: "9px 18px 10px 18px !important",
    border: "none !important",
    boxShadow:
      "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
    borderRadius: "8px !important",
    color: "#D9D9D9 !important",
    textTransform: "none !important",
    "@media screen and (maxWidth: 400px)": {
      fontSize: "0.4rem",
    },
    fontFamily: "Inter !important",
  },
  nextActionButton: {
    backgroundColor: "#3DB76E !important",
    color: "white !important",
  },
}))

const NewUserModal = ({
  open,
  setOpen,
  usuariosid,
  usuariosemail,
  usuariosrol,
  usuariosnombre,
  usuariosstatus,
  callback,
}) => {
  const [errorCorreoText, setErrorCorreoText] = useState("")
  const [errorNombreText, setErrorNombreText] = useState("")
  const [erroroll, setErroroll] = useState("")
  const [errorvalor, setErrorvalor] = useState(false)
  const { showNotification } = useApp()

  const formClasses = classUseStyles()
  const { control, handleSubmit, reset, setValue } = useForm({
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      rollusuario: "",
    },
  })

  const setUsuariosData = (usunomb, ususem, usuarol) => {
    setValue("email", ususem)
    setValue("firstName", usunomb)
    setValue("rollusuario", usuarol)
  }

  const useStylesOption = makeStyles({
    option: {
      "&:hover": {
        backgroundColor: "#DAE6F1 !important",
        color: "#0957A0",
        font: "Inter",
        size: "16px",
      },
    },
  })

  const useStyles = makeStyles(() => ({
    longitudIcon: {
      "& svg": {
        width: "24px",
        height: "22px",
      },
    },
  }))

  const text = localStorage.getItem("current-monumental-user")
  let rollocal = JSON.parse(text)

  const textInput = React.useRef(null)

  const classes = useStyles()

  const theme = useTheme()

  const fullScreen = useMediaQuery(theme.breakpoints.down("xs"))

  const [shoNewUserModal, setShowNewUserModal] = useState(false)

  const [roll, setAge] = useState("")

  const [status, setStatus] = useState("")

  const [titmessage, setTitmessage] = useState("")

  useEffect(() => {
    setAge(usuariosrol)
    setStatus(usuariosstatus)

    setUsuariosData(usuariosnombre, usuariosemail, usuariosrol)
  }, [open])

  const onSubmit = async (data) => {
    try {
      var algunerror = ""

      if (!data.firstName) {
        setErrorNombreText("Campo obligatorio")
        algunerror = "e"
      }

      if (!roll) {
        setErrorvalor(true)
        setErroroll("Campo obligatorio")
        algunerror = "e"
      }

      if (data.email.length <= 0) {
        setErrorCorreoText("Campo obligatorio")
        algunerror = "e"
      }

      const textocorreo = data.email.indexOf("@")
      var subStrtextocorreo = data.email.substr(0, textocorreo)

      if (subStrtextocorreo.length > 64 && data.email.length > 1) {
        setErrorCorreoText("Correo electrónico inválido")
        algunerror = "e"
      }

      if (
        email_regex.test(data.email) === false &&
        data.email.length > 1 &&
        email_regex.test(data.email) === false
      ) {
        setErrorCorreoText("Correo electrónico inválido")
        algunerror = "e"
      }

      if (algunerror === "") {
        if (usuariosid === undefined) {
          await createUser(
            data.email,
            data.firstName,
            data.lastName,
            roll
          )
          callback()
          //console.log("El usuario "  + data.firstName + " ha sido creado y activado satisfactoriamente.");
          handleClose()
          showNotification(
            "El usuario fue creado exitosamente. Se ha enviado un correo electrónico con instrucciones para completar el proceso de registro",
            "success"
          )
        } else {
          const updateEmailData = {
            email: data.email,
          }

          const updateUsuariosData = {
            firstName: data.firstName,
            lastName: "",
            role: roll,
          }

          if (usuariosemail !== data.email) {
            await updateUserEmail(usuariosid, updateEmailData)
            callback()
          }

          await updateUsers(usuariosid, updateUsuariosData)
          callback()

          //console.log("despues de up DATOS USUARIOS NOMBRE: " + updateUsuariosData );

          handleClose()
          showNotification(
            "El usuario fue actualizado exitosamente.",
            "success"
          )
        }
      }
    } catch (error) {
      if (error.response === undefined) {
        if (usuariosid === undefined) {
          showNotification(
            "Lo sentimos, se ha producido un error inesperado al crear un nuevo  usuario",
            "error"
          )
        } else {
          showNotification(
            "Lo sentimos se ha producido un error inesperado al editar la información del usuario",
            "error"
          )
        }
      } else {
        if (usuariosid === undefined) {
          if (
            error.response.data.message ===
            "Este Correo ya se encuentra registrado en el sistema."
          ) {
            showNotification(
              "Lo sentimos, la dirección de correo electrónico que ha ingresado ya está registrada",
              "error"
            )
          }
          if (error.message.indexOf("500") > 0) {
            showNotification(
              "Lo sentimos, se ha producido un error inesperado al crear un nuevo  usuario",
              "error"
            )
          }
        } else {
          if (
            error.response.data.message ===
            "Error al editar el Usuario, el correo ya se encuentra registrado."
          ) {
            showNotification(
              "Lo sentimos, la dirección de correo electrónico que ha ingresado ya está registrada",
              "error"
            )
          } else {
            if (error.message.indexOf("500") > 0) {
              showNotification(
                "Lo sentimos se ha producido un error inesperado al editar la información del usuario",
                "error"
              )
            }
          }
        }
      }
    }
  }

  const handleClose = () => {
    reset()
    setAge("")
    setErrorCorreoText("")
    setErroroll("")
    setErrorvalor(false)
    setErrorNombreText("")
    setOpen(false)
    callback()
  }

  const handleChange = (event) => {
    setAge(event.target.value)
  }

  const handleChangeestatus = (event) => {
    setStatus(event.target.value)
  }

  const estilocajitas = {
    marginLeft: "49px",
  }

  const estiloboton = {
    fontSize: "14px !important",
    fontWeight: "600 !important",
    lineHeight: "28px !important",
    backgroundColor: "#FFFFFF !important",
    padding: "9px 18px 10px 18px !important",
    border: "none !important",
    boxShadow:
      "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
    borderRadius: "8px !important",
    color: "#D9D9D9 !important",
    textTransform: "none !important",
    "@media screen and (maxWidth: 400px)": {
      fontSize: "0.4rem",
    },
    fontFamily: "Inter !important",
  }

  const DialogActionspadding = {
    justifyContent: "center",
    alignItems: "center",
    marginTop: "34px",
    gap: "20px",
    marginBottom: "20px",
  }

  const modalestilo = {
    width: "530px !important",
    maxWidth: "530px !important",
    borderRadius: "8px",
  }

  const textotitulomodal = {
    fontFamily: "Inter",
    fontSize: "24px",
    fontStyle: "normal",
    fontWeight: "600",
    lineHeight: "24px",
    letterSpacing: "0.15px",
    color: "#363636",
    marginTop: "8px",
    marginLeft: "-10px",
    marginBottom: "6px",
    textAlign: "center",
  }

  const formaestilo = {
    borderTop: "1px solid #979797",
  }

  const dialogoestilo = {
    marginTop: "34px",
    overflowY: "hidden",
  }

  const iconobucador = {
    color: "#0957A0",
    marginRight: "8px",
    fontSize: "28px",
  }


  const useStyleselect = makeStyles({
    container: {
      marginTop: "0%",
    },
    formControl: {
      minWidth: 120,
    },
    label: {
      "& .MuiFormLabel-root": {
        color: "rgba(0, 0, 0, 0.87)",
      },
      "&.Mui-focused": {
        color: "rgba(0, 0, 0, 0.87)",
      },
    },
    select: {
      "&:after": {
        borderBottomColor: "darkred",
      },
      "& .MuiSvgIcon-root": {
        color: "#009EFF",
        fontSize: "3.2rem",
        height: "56px",
        borderLeft: "1px solid #C4C4C4",
        borderRadius: "1",
        right: "0px",
        top: "0px",
        width: "60px",
      },
      "&:click ": {
        color: "red",
      },
    },
  })

  const useStylelabel = makeStyles({
    label: {
      color: "rgba(0, 0, 0, 0.87)",
      "&.Mui-focused": {
        color: "rgba(0, 0, 0, 0.87)",
      },
      fontFamily: "Inter",
      fontStyle: "normal",
      fontWeight: "400",
      fontSize: "16px",
      lineHeight: "24px",
      letterSpacing: "0.15px",
    },
  })

  const classeselect = useStyleselect()

  const classeslabel = useStylelabel()

  const stylesoption = useStylesOption()

  const [searchIconcolor, setSearchIconcolor] = useState(false)

  const handleFocusEmail = (event) => {
    event.preventDefault()
    const { target } = event
    setErrorCorreoText("")
  }

  const handleFocusNombre = (event) => {
    event.preventDefault()
    const { target } = event
    setErrorNombreText("")
  }

  const handleFocusRoll = (event) => {
    event.preventDefault()
    const { target } = event
    setErrorvalor(false)
    setErroroll("")
  }


  return (
    <Dialog
      open={open}
      PaperProps={{
        sx: modalestilo,
      }}
    >
      <DialogTitle>
        <h2 style={textotitulomodal}>
          {usuariosid === undefined ? "Nuevo usuario" : "Editar usuario"}
        </h2>
      </DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)} id="datos" style={formaestilo}>
        <DialogContent style={dialogoestilo}>
          <Controller
            name="firstName"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                autoFocus
                margin="dense"
                id="firstName"
                inputRef={textInput}
                label="Nombre y Apellido&#160;"
                onFocus={handleFocusNombre}
                inputProps={{ maxLength: 30 }}
                sx={{
                  "& label": {
                    color: "rgba(0, 0, 0, 0.87)",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: "400",
                    fontSize: "16px",
                    lineHeight: "24px",
                    letterSpacing: "0.15px",
                  },
                  height: "58px",
                  width: "80%",
                  marginLeft: 6.5,
                  marginTop: 0.0,
                }}
                type="text"
                fullWidth
                variant="outlined"
                error={errorNombreText}
                helperText={errorNombreText}
                {...field}
              />
            )}
          />

          <Controller
            name="email"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                margin="dense"
                id="email"
                type="text"
                label="Correo electrónico&#160;"
                inputRef={textInput}
                onFocus={handleFocusEmail}
                fullWidth
                variant="outlined"
                className={classeslabel.label}
                sx={{
                  "& label": {
                    color: "rgba(0, 0, 0, 0.87)",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: "400",
                    fontSize: "16px",
                    lineHeight: "24px",
                    letterSpacing: "0.15px",
                  },
                  height: "58px",
                  marginTop: 3.5,
                  width: "80%",
                  marginLeft: 6.5,
                }}
                error={errorCorreoText}
                helperText={errorCorreoText}
                {...field}
              />
            )}
          />

          <Box
            sx={{
              "& label": {
                color: "rgba(0, 0, 0, 0.87)",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: "400",
                fontSize: "16px",
                lineHeight: "24px",
                letterSpacing: "0.15px",
              },
              height: "58px",
              width: "80%",
              marginTop: 3.5,
              marginLeft: 6.5,
            }}
          >
            <FormControl fullWidth error={errorvalor}>
              <InputLabel id="roll-usuario">Rol</InputLabel>
              <Select
                labelId="roll-usuario"
                id="rollusuario"
                value={roll}
                label={"Rol"}
                onChange={handleChange}
                onFocus={handleFocusRoll}
                className={classeselect.select}
              >
              
                 
              
                <MenuItem
                  disabled={rollocal.role !== "SUPER_ADMIN"}
                  value="SUPER_ADMIN"
                >Super Administrador
                  </MenuItem>
                     <MenuItem value="ADMINISTRADOR">Administrador</MenuItem>
                <MenuItem value="MODERADOR">Moderador</MenuItem>
                <MenuItem value="TICKER">Cintillo</MenuItem>
              
                {rollocal.role === "SUPER_ADMIN" ? (
                <MenuItem value="TRIVIA_ADMIN">Administrador Trivia</MenuItem>
               )
                :
                (<React.Fragment>

                </React.Fragment>)}
               
              </Select>
              <FormHelperText>{erroroll}</FormHelperText>
            </FormControl>
          </Box>
          <div style={{ display: "flex", alignItems: "flex-end" }}></div>
        </DialogContent>

        <DialogActions style={DialogActionspadding}>
          {usuariosid === undefined && (
            <React.Fragment>
              <SecundaryButton
                text="Cancelar"
                action={handleClose}
                width="125px"
                height="40px"
                fontSize="14"
                lineHeight="28"
                type="button"
                noShadow={true}
              />
              <PrimaryButton
                text="Crear"
                action={() => {}}
                width="125px"
                height="40px"
                fontSize="14"
                lineHeight="28"
                type="submit"
                noShadow={true}
              />
            </React.Fragment>
          )}
          {usuariosid !== undefined && (
            <React.Fragment>
              <SecundaryButton
                text="Cancelar"
                action={handleClose}
                width="125px"
                height="40px"
                fontSize="14"
                lineHeight="28"
                type="button"
                noShadow={true}
              />
              <PrimaryButton
                text="Guardar"
                action={() => {}}
                width="125px"
                height="40px"
                fontSize="14"
                lineHeight="28"
                type="submit"
                noShadow={true}
              />
            </React.Fragment>
          )}
        </DialogActions>
      </form>
    </Dialog>
  )
}

NewUserModal.propTypes = {
  open: PropTypes.bool.isRequired,
  setOpen: PropTypes.func.isRequired,
  usuariosid: PropTypes.string,
  usuariosemail: PropTypes.string,
  usuariosrol: PropTypes.string,
  usuariosnombre: PropTypes.string,
  posts: PropTypes.array,
  otraprueba: PropTypes.string,
  usuariosstatus: PropTypes.string,
  callback: PropTypes.func.isRequired,
}

export default NewUserModal
