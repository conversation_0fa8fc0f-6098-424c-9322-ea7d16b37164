import {
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material"
import { makeStyles } from "@mui/styles"
import PropTypes from "prop-types"

const useStyles = makeStyles(() => ({
  primaryButton: {
    borderRadius: "8px",
    border: "2px solid #009EFF",
    background: "#009EFF",
    "&:hover": {
      cursor: "pointer",
    },
  },
}))

const PrimaryButton = ({
  text,
  action,
  width,
  height,
  fontSize,
  type,
  margin,
  noShadow,
  lineHeight,
  startIcon,
  onKeyDown,
  cs
}) => {
  const classes = useStyles()
  let lineHeight2 = lineHeight ? lineHeight : parseInt(fontSize) + 5
  fontSize = fontSize + "px"
  lineHeight2 = lineHeight2 + "px"

  return (
    <button
      className={classes.primaryButton}
      onClick={action}
      onKeyDown={onKeyDown}
      type={type}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center", 
        flexDirection: "row",
        width: width ? width : "fit-content",
        height: height ? height : "fit-content",
        padding: width ? "0px" : "2.9px 20px",
        margin: margin ? margin : "0px",
        boxShadow: noShadow ? "none" : "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
        ...cs
      }}
    >
      {startIcon && startIcon}
      <Typography
        style={{
          fontWeight: "700",
          fontSize: fontSize,
          lineHeight: lineHeight2,
          letterSpacing: "0.15px",
          fontFamily: "Inter",
          color: "#FFFFFF",
        }}
      >
        {text}
      </Typography>
    </button>
  )
}

PrimaryButton.propTypes = {
  text: PropTypes.string,
  action: PropTypes.func,
  fontSize: PropTypes.string.isRequired,
  width: PropTypes.string,
  height: PropTypes.string,
  type: PropTypes.string,
  margin: PropTypes.string,
  noShadow: PropTypes.bool,
  lineHeight: PropTypes.string,
  startIcon: PropTypes.element,
  onKeyDown: PropTypes.func,
  cs: PropTypes.object
}

export default PrimaryButton
