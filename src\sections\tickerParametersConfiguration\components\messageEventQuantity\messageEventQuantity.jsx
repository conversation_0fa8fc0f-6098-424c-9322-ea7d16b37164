import { Grid, Typography } from "@mui/material"
import PropTypes from "prop-types"
import Icon from "../../../../assets/exclamation.png"
import { makeStyles } from "@mui/styles"
import { useEffect, useState } from "react"

const useStyles = makeStyles(() => ({
  
  iconStyle: {
    position: "absolute",
    left: "6px",
    top: "-14px",
    width: "24px",
    height: "26px",
  },
}))

const MessageEventQuantity = ({ eventDuration, smsDuration}) => {
  const classes = useStyles()


  return (
    <Grid
      item
      container
      direction={"row"}
      gap={"10px"}
      position={"absolute"}
      right={"83px"}
      top={"142px"}
      width={"fit-content"}
    >
      <Grid
        item
        container
        direction={"column"}
        borderRadius={"10px"}
        bgcolor={"rgba(54, 54, 54, 0.1)"}
        width={"166px"}
        height={"71px"}
        gap={"1px"}
        textAlign={"center"}
        justifyContent={"center"}
      >
        <img alt="icon" src={Icon} className={classes.iconStyle} />
        <Typography
          sx={{
            fontWeight: "500",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "25px",
          }}
        >
          {Math.floor((eventDuration * 60) / smsDuration)}
        </Typography>
        <Typography
          sx={{
            fontWeight: "500",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "12px",
            textAlign: "center",
          }}
        >
          Cantidad max de SMS a mostrar en este evento
        </Typography>
      </Grid>
    </Grid>
  )
}

MessageEventQuantity.propTypes = {
  eventDuration: PropTypes.number.isRequired,
  smsDuration: PropTypes.number.isRequired,
}

export default MessageEventQuantity
