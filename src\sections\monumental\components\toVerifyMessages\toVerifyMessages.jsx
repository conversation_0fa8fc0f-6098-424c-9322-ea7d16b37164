import React, { useState, useEffect } from "react"
import { <PERSON>ton, <PERSON>rid, Typography } from "@mui/material"
import PropTypes from "prop-types"
import { MonumentalMessagesSocketEvents } from "../../socket-events/monumental-socket-events"
import MessageCard from "../messageCard"
import { motion, AnimatePresence } from "framer-motion"
import { useDebouncedWindowSize } from "../../custom-hooks/debounced-window-size"
import { useGeneralMessageSocketManagement } from "../../custom-hooks/general-message-socket-management-hook"
import { messageVariants } from "../../animations/message-animation"

const ToVerifyMessages = ({
  socket,
  on,
  emit,
  off,
  inappropiateQuantity,
}) => {
  const showMoreMessagesBreakPoint = 1850

  const windowSize = useDebouncedWindowSize()

  const [messages, setMessages] = useState([])

  const [quantity, setQuantity] = useState(1)

  const [nextPage, setNextPage] = useState(false)

  const [page, setPage] = useState(1)

  const [prevPageLast, setPrevPageLast] = useState([
    {
      message: null,
      page: 0,
    },
  ])

  const numberElements = windowSize.width >= showMoreMessagesBreakPoint ? 12 : 9

  const {
    readyToSend,
    acceptAllTimeout,
    handleAproveAllMessages,
    handleApproveMessage,
    handleRejectMessage,
    setAlreadyFetchedMessages,
    alreadyFetchedMessages,
  } = useGeneralMessageSocketManagement(
    messages,
    setMessages,
    socket,
    emit,
    on,
    off,
    MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES_INAPPROPIATE,
    quantity,
    MonumentalMessagesSocketEvents.SEND_MESSAGES_TO_MODERATOR_INAPPROPIATE,
    numberElements,
    numberElements,
    MonumentalMessagesSocketEvents.MODERATOR_IS_FULL_INAPPROPIATE,
    prevPageLast,
    page
  )

  useEffect(() => {
    if (!readyToSend) return
    emit(
      MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES_INAPPROPIATE,
      {
        quantity: numberElements,
      }
    )
    setAlreadyFetchedMessages(true)
  }, [readyToSend])

  useEffect(() => {
    if (
      inappropiateQuantity -
        page * (numberElements) >
      0
    )
      setNextPage(true)
    else setNextPage(false)
  }, [inappropiateQuantity, page, windowSize])

  useEffect(() => {
    console.log(messages.length)
    if (alreadyFetchedMessages) return
    if (page > 1 && messages.length == 0) handlePrevPage()
  }, [messages])

  useEffect(() => {
    if (!readyToSend) return
    if (alreadyFetchedMessages) return
    if (
      windowSize.width >= showMoreMessagesBreakPoint &&
      messages.length < 12
    ) {
      const pagination =
        messages.length > 0
          ? {
              last_msg_id: messages[messages.length - 1].msg_id,
              last_msg_date: messages[messages.length - 1].msg_date,
            }
          : {}
      emit(
        MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES_INAPPROPIATE,
        {
          quantity: 12 - messages.length,
          ...pagination,
        }
      )
      setAlreadyFetchedMessages(true)
    }

    if (
      windowSize.width <= showMoreMessagesBreakPoint &&
      messages.length > 9
    ) {
      setMessages((prev) => prev.slice(0,9))
    }
    
  }, [windowSize])

  const handleNextPage = () => {
    setPrevPageLast((prev) => {
      const auxList = prev.filter((p) => p.page != page)
      auxList.push({
        message: messages[messages.length - 1],
        page: page,
      })
      return auxList
    })
    const pagination =
      messages.length > 0
        ? {
            last_msg_id: messages[messages.length - 1].msg_id,
            last_msg_date: messages[messages.length - 1].msg_date,
          }
        : {}
    emit(
      MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES_INAPPROPIATE,
      {
        quantity: numberElements,
        ...pagination,
      }
    )
    setAlreadyFetchedMessages(true)
    setMessages([])
    setPage((prev) => prev + 1)
  }

  const handlePrevPage = () => {
    const lastMessage = prevPageLast.find((p) => p.page == page - 2).message
    const pagination = lastMessage
      ? {
          last_msg_id: lastMessage.msg_id,
          last_msg_date: lastMessage.msg_date,
        }
      : {}
    emit(
      MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES_INAPPROPIATE,
      {
        quantity: numberElements,
        ...pagination,
      }
    )
    setAlreadyFetchedMessages(true)
    setMessages([])
    setPage((prev) => prev - 1)
  }

  return (
    <React.Fragment>
      <Grid
        container
        direction="column"
        width={`${
          windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
        }px`}
      >
        <div
          style={{
            alignSelf: "flex-end",
            padding: "0 14px 58px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            gap: "13px",
          }}
        >
          <Typography
            sx={{
              color: "black",
              fontStyle: "normal",
              fontFamily: "Inter",
              fontSize: "13px",
              fontWeight: "500",
              padding: "0 !important",
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Aprobar todos los SMS (
            {windowSize.width >= showMoreMessagesBreakPoint
              ? messages.length > 12
                ? 12
                : messages.length
              : messages.length > 9
              ? 9
              : messages.length}
            )
          </Typography>
          <Button
            sx={{
              background: "#3DB76E",
              borderRadius: "8px",
              width: "127px",
              height: "36px",
              color: "#FFFF",
              fontStyle: "normal",
              fontFamily: "Inter",
              fontSize: "15px",
              fontWeight: "500",
              padding: "0 !important",
              fontFeatureSettings: "'liga' off, 'clig' off",
              textTransform: "none",
              "&:hover": {
                background: "#3DB75E",
              },
            }}
            disabled={messages.length == 0 || acceptAllTimeout}
            onClick={() =>
              handleAproveAllMessages(
                numberElements
              )
            }
          >
            Aprobar Todos
          </Button>
        </div>
        <Grid
          container
          direction="row"
          padding={"0 0 0 14px"}
          alignSelf={"flex-start"}
          gap={"17px 21px"}
          width={`${
            windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
          }px`}
        >
          {messages.length == 0 && (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                width: "100%",
              }}
            >
              <Typography
                sx={{
                  color: "#00182F",
                  fontStyle: "normal",
                  fontFamily: "Inter",
                  fontSize: "20px",
                  fontWeight: "400",
                  padding: "0 !important",
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                No hay resultados que mostrar
              </Typography>
            </div>
          )}
          <AnimatePresence>
            {messages
              .slice(0, numberElements)
              .map(({ msg_id, msg_date, msg_content }, ind) => {
                return (
                  <motion.div
                    key={msg_id}
                    variants={messageVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    layout // Enables smooth layout animations when items reorder
                    style={{ originX: 0 }} // For scale animation origin
                  >
                    <MessageCard
                      msg_id={msg_id}
                      msg_date={msg_date}
                      msg_content={msg_content}
                      handleApproveMessage={handleApproveMessage}
                      handleRejectMessage={handleRejectMessage}
                    />
                  </motion.div>
                )
              })}
          </AnimatePresence>
        </Grid>
        {messages.length > 0 && (
          <Grid
            item
            container
            direction={"row"}
            margin={"40px 0 0 0"}
            width={`${
              windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
            }px`}
            height={"10px"}
            justifyContent={"end"}
            gap={"19px"}
          >
            <a
              style={{
                fontFamily: "Inter",
                fontSize: "13px",
                fontWeight: "500",
                letterSpacing: "0.15px",
                cursor: page == 1 || acceptAllTimeout ? "none" : "pointer",
                pointerEvents: page == 1 || acceptAllTimeout ? "none" : "auto",
                color: page == 1 || acceptAllTimeout ? "#E3E2E2" : "black",
              }}
              onClick={() => handlePrevPage()}
            >
              Anterior
            </a>
            <div
              style={{
                fontFamily: "Inter",
                fontSize: "13px",
                fontWeight: "500",
                gap: "18px",
                letterSpacing: "0.15px",
                cursor: !nextPage || acceptAllTimeout ? "none" : "pointer",
                pointerEvents: !nextPage || acceptAllTimeout ? "none" : "auto",
                color: !nextPage || acceptAllTimeout ? "#E3E2E2" : "black",
                display: "flex",
                alignItems: "center",
              }}
              onClick={() => handleNextPage()}
            >
              Siguiente{" "}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="7"
                height="12"
                viewBox="0 0 7 12"
                fill="none"
              >
                <path
                  d="M7.09668 6.07797L6.51554 5.46952L1.2648 -2.5492e-07L-2.62792e-05 1.21689L4.66938 6.07813L-2.67042e-05 10.9394L1.2648 12.1563L6.51554 6.68673L7.09668 6.07797Z"
                  fill={!nextPage || acceptAllTimeout ? "#E3E2E2" : "black"}
                />
              </svg>
            </div>
          </Grid>
        )}
      </Grid>
    </React.Fragment>
  )
}

ToVerifyMessages.propTypes = {
  socket: PropTypes.object,
  on: PropTypes.func.isRequired,
  emit: PropTypes.func.isRequired,
  off: PropTypes.func.isRequired,
  inappropiateQuantity: PropTypes.number.isRequired,
}

export default ToVerifyMessages
