import { Grid, Typography } from "@mui/material"
import PropTypes from "prop-types"
import bellIcon from "../../../../assets/notifications.svg"
import { makeStyles } from "@mui/styles"
import { useEffect, useState } from "react"

const useStyles = makeStyles(() => ({
  "@global": {
    "@keyframes bell-swing": {
      "0%": { transform: "rotate(0deg)" },
      "15%": { transform: "rotate(-15deg)" },
      "30%": { transform: "rotate(10deg)" },
      "45%": { transform: "rotate(-10deg)" },
      "60%": { transform: "rotate(6deg)" },
      "75%": { transform: "rotate(-4deg)" },
      "100%": { transform: "rotate(0deg)" },
    },
  },
  bellAnimation: {
    animation: "bell-swing 1s ease-in-out",
    transformOrigin: "60% 0%",
    position: "absolute",
    left: "-14px",
    top: "-11px",
    width: "31px",
    height: "31px",
  },
}))

const MessageQuantity = ({ newQuantity, innapropiateQuantity, approvedQuantity, changed}) => {
  const classes = useStyles()
  const [bellKey, setBellKey] = useState(0)

  // Retrigger animation when newQuantity changes
  useEffect(() => {
    setBellKey(prev => prev + 1)
  }, [changed])

  return (
    <Grid
      item
      container
      direction={"row"}
      gap={"10px"}
      position={"absolute"}
      right={"83px"}
      top={"132px"}
      width={"fit-content"}
    >
      <Grid
        item
        container
        direction={"column"}
        borderRadius={"10px"}
        bgcolor={"rgba(54, 54, 54, 0.1)"}
        width={"98px"}
        height={"52px"}
        gap={"1px"}
        textAlign={"center"}
        justifyContent={"center"}
      >
        <img alt="bell-icon" key={bellKey} src={bellIcon} className={classes.bellAnimation} />
        <Typography
          sx={{
            fontWeight: "500",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "22px",
          }}
        >
          {newQuantity}
        </Typography>
        <Typography
          sx={{
            fontWeight: "400",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "12px",
          }}
        >
          SMS para hoy
        </Typography>
      </Grid>

      <Grid
        item
        container
        direction={"column"}
        borderRadius={"10px"}
        bgcolor={"rgba(54, 54, 54, 0.1)"}
        width={"98px"}
        height={"52px"}
        gap={"1px"}
        textAlign={"center"}
        justifyContent={"center"}
      >
        <Typography
          sx={{
            fontWeight: "500",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "22px",
          }}
        >
          {innapropiateQuantity > 99 ? "+99" : innapropiateQuantity}
        </Typography>
        <Typography
          sx={{
            fontWeight: "400",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "11px",
          }}
        >
          SMS por verificar
        </Typography>
      </Grid>
      <Grid
        item
        container
        direction={"column"}
        borderRadius={"10px"}
        bgcolor={"rgba(54, 54, 54, 0.1)"}
        width={"98px"}
        height={"52px"}
        gap={"1px"}
        textAlign={"center"}
        justifyContent={"center"}
      >
        <Typography
          sx={{
            fontWeight: "500",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "22px",
          }}
        >
          {approvedQuantity}
        </Typography>
        <Typography
          sx={{
            fontWeight: "400",
            fontFamily: "Inter",
            lineHeight: "normal",
            fontSize: "12px",
          }}
        >
          SMS publicados
        </Typography>
      </Grid>
    </Grid>
  )
}

MessageQuantity.propTypes = {
  newQuantity: PropTypes.number.isRequired,
  innapropiateQuantity: PropTypes.number.isRequired,
  approvedQuantity: PropTypes.number.isRequired,
  changed: PropTypes.number.isRequired,
}

export default MessageQuantity
