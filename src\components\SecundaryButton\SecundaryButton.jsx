import {
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material"
import { makeStyles } from "@mui/styles"
import PropTypes from "prop-types"

const useStyles = makeStyles(() => ({
  secundaryButton: {
    borderRadius: "8px",
    background: "white",
    border: "2px solid #009EFF",
    "&:hover": {
      cursor: "pointer",
    },
    "&:disabled": {
      border: "2px solid #98e4f0",
    }
  },
}))

const SecundaryButton = ({
  text,
  action,
  width,
  height,
  fontSize,
  type,
  margin,
  noShadow,
  lineHeight,
  startIcon,
  disabled,
}) => {
  const classes = useStyles()
  let lineHeight2 = lineHeight ? lineHeight : parseInt(fontSize) + 5
  fontSize = fontSize + "px"
  lineHeight2 = lineHeight2 + "px"

  return (
    <button
      className={classes.secundaryButton}
      onClick={action}
      type={type}
      disabled={disabled ? disabled : false}
      style={{
        width: width ? width : "fit-content",
        height: height ? height : "fit-content",
        padding: width ? "0px" : "2.9px 20px 2.9px 20px",
        margin: margin ? margin : "0px",
        boxShadow: noShadow ? "none" : "0px 4px 4px 0px rgba(0, 0, 0, 0.25)" ,
      }}
    >
      {startIcon && startIcon}
      <Typography
        style={{
          fontWeight: "700",
          fontSize: fontSize,
          lineHeight: lineHeight2,
          letterSpacing: "0.15px",
          fontFamily: "Inter",
          color: "#009EFF",
          ...disabled && { color: "#98e4f0" }
        }}
      >
        {text}
      </Typography>
    </button>
  )
}

SecundaryButton.propTypes = {
  text: PropTypes.string,
  action: PropTypes.func,
  fontSize: PropTypes.string.isRequired,
  width: PropTypes.string,
  height: PropTypes.string,
  type: PropTypes.string,
  margin: PropTypes.string,
  noShadow: PropTypes.bool,
  lineHeight: PropTypes.string,
  startIcon: PropTypes.element,
  disabled: PropTypes.bool,
}

export default SecundaryButton
