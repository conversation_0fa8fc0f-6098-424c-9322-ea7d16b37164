import { useState, useEffect } from "react"
import * as React from "react"
import { Grid, Ty<PERSON>graphy, Button, TextField, Box } from "@mui/material"
import { useApp } from "../../AppProvider"
import { useNavigate } from "react-router"
import { makeStyles } from "@mui/styles"
import { useForm } from "react-hook-form"
import logoStock from "./../../assets/ConectiumFullLogo.svg"
import monumentalLogo from "./../../assets/monumentalLogo.png"
import { recoverPassword } from "./services/api"
import { email_regex } from "../../helpers/regular_expressions"
import fondoLogin from "./../../assets/fondo-login-monumental.PNG"
import PrimaryButton from "../../components/PrimaryButton"

const useStyles = makeStyles(() => ({
  root: {
    height: "calc(100vh - 180px)",
    backgroundImage: `url(${fondoLogin})`,
    fontFamily: "Inter, sans-serif",
    padding: "90px 0",
    overflowY: "hidden",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
  recoverRoot: {
    height: "100vh",
    backgroundColor: "#FFFFFF",
    fontFamily: "Inter, sans-serif",
  },
  loginIcon: {
    width: "40vw !important",
    marginBottom: "100px !important",
  },

  baseFrame: {
    height: "fit-content",
    width: "555px",
  },
  formInput: {
    "& .MuiOutlinedInput-root": {
      "& input:-webkit-autofill": {
        "-webkit-text-fill-color": "white",
        "-webkit-box-shadow": "0 0 0px 1000px transparent inset",
        transition: "background-color 5000s ease-in-out 0s",
      },
      "& input::placeholder": {
        color: "white",
        opacity: 1,
      },
      "& fieldset": {
        borderRadius: "8px",
        borderColor: "white",
      },
      "&.Mui-focused fieldset": {
        borderColor: "white",
      },
      "&:hover fieldset": {
        borderColor: "white !important",
      },
      "&.Mui-error fieldset": {
        borderColor: "white",
      },
    },
    "& .MuiInputBase-root": {
      height: "50px !important",
      width: "373px !important",
    },
    "& .MuiInputPlaceholder-root": {
      color: "white",
      fontFamily: "Inter",
      fontSize: "16px !Important",
      fontWeight: "500",
    },
    "& .Mui-focused": {
      color: "white !important",
    },
    "& .Mui-error": {
      color: "#d32f2f !important",
    },
  },
  phoneImage: {
    position: "absolute",
    height: "80%",
    marginRight: "8%",
  },
  titleText: {
    color: "#F5F5F5",
    margin: 0,
    fontWeight: "400",
    fontFamily: "Inter, sans-serif",
    fontSize: "35px",
    "& b": {
      color: "#009EFF",
      fontWeight: "700",
    },
    "& strong": {
      color: "#F5F5F5",
      margin: 0,
      fontWeight: "700",
      fontFamily: "Inter, sans-serif",
      fontSize: "35px",
    },
    ["@media (max-width: 1370px)"]: {
      fontSize: 30,
    },
  },
  titleImage: {
    width: "35vw",
    position: "absolute",
    bottom: "10vh",
    right: "45.9%",
  },
  PasswordIcon: {
    objectFit: "cover",
    width: "100%",
    height: "100%",
    background: "rgba(6 153 226)",
  },
  esmsLogo: {
    width: "320px",
    marginBottom: "56px !important",
  },
  acceptButton: {
    borderColor: "#4E5589 !important",
    textTransform: "none !important",
    borderWidth: "2px !important",
    borderRadius: "8px !important",
    color: "#4E5589 !important",
    height: "53px !important",
    "&:hover": {
      /* background: " linear-gradient( #008C0D 33.74%, #00BF19 96.06%);", */
      background: "#4E5589 !important",
      boxShadow:
        "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
      color: "white !important",
    },
  },
  blueButton: {
    background: "#4E5589 !important",
    borderColor: "#4E5589 !important",
    textTransform: "none !important",
    borderWidth: "2px !important",
    borderRadius: "8px !important",
    color: "white !important",
    height: "53px !important",
  },
  formFrame: {
    background: "rgba(231, 231, 231, 0.20)",
    borderRadius: "20px",
    height: "fit-content",
    width: "555px",
    display: "flex",
    padding: "56px 0px",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: "130px",
  },
  monumentalLogo: {
    width: "221px",
    height: "72.28px",
  },
}))

const RecoverPassword = () => {
  const navigate = useNavigate()
  const classes = useStyles()
  const { showNotification, currentUser, clearCurrentUser } = useApp()
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()
  const { email: emailError } = errors
  const [recoverSuccess, setRecoverSuccess] = useState(false)
  const [userEmail, setUserEmail] = useState("")

  useEffect(async () => {
    if (currentUser) {
      clearCurrentUser()
    }
  }, [currentUser])

  const onSubmit = async ({ email }) => {
    const response = await recoverPassword(email)
      .then((res) => {
        setUserEmail(email)
        setRecoverSuccess(true)
      })
      .catch((err) => {
        switch (err?.response?.status) {
          case 400:
            showNotification(
              "Lo sentimos, el correo electrónico de recuperación de contraseña no pudo ser enviado al destinatario",
              "error"
            )
            break
          case 403:
            showNotification(
              "Tu usuario no ha sido verificado. Comunícate con el administrador para crear tus credenciales de acceso",
              "error"
            )
            break
          default:
            showNotification(
              "Lo sentimos, el correo electrónico de recuperación de contraseña no pudo ser enviado al destinatario",
              "error"
            )
        }
      })
  }

  return (
    <Box className={classes.root}>
      <Grid item container className={classes.esmsLogo} justifyContent="center">
        <img alt="andromeda" src={logoStock} style={{ height: "100%" }} />
      </Grid>
      <Grid
        item
        className={classes.baseFrame}
        xs={6.6}
        sm={6.6}
        md={6.6}
        container
        direction="column"
        alignItems="center"
      >
        <div className={classes.formFrame}>
          <Typography
            item
            style={{
              textAlign: "center",
              marginBottom: "25px",
              letterSpacing: "0.15px",
              fontFamily: "Inter, sans-serif",
              fontSize: "24px",
              fontWeight: "600",
              lineHeight: "24px",
              padding: 0,
              color: "#FFFFFF",
            }}
          >
            Recuperar contraseña
          </Typography>
          <Grid item container direction="column" alignItems="center">
            {recoverSuccess === false ? (
              <Grid item>
                <Typography
                  style={{
                    fontFamily: "Inter",
                    fontSize: "16px",
                    fontWeight: "600",
                    lineHeight: "24px",
                    letterSpacing: "0.15px",
                    marginBottom: "40px",
                    textAlign: "center",
                    color: "#FFFFFF",
                  }}
                >
                  Las instrucciones de recuperación de contraseña <br />
                  se enviarán al correo electrónico ingresado a <br />{" "}
                  continuación:
                </Typography>
              </Grid>
            ) : (
              <React.Fragment></React.Fragment>
            )}

            {recoverSuccess === false ? (
              <form onSubmit={handleSubmit(onSubmit)} style={{ width: "100%" }}>
                <Grid
                  item
                  container
                  direction="column"
                  alignItems="center"
                  spacing={2}
                >
                  <Grid
                    item
                    container
                    direction="row"
                    xs={8}
                    sm={8}
                    md={8}
                    justifyContent="center"
                  >
                    <Grid item container justifyContent="center">
                      <TextField
                        className={classes.formInput}
                        inputProps={{
                          style: {
                            background: "transparent",
                            textAlign: "center",
                            "&::placeholder": {
                              color: "white",
                            },
                            color: "white",
                          },
                        }}
                        sx={{
                          marginBottom: "25px !important",
                        }}
                        id="email"
                        placeholder="Correo electrónico"
                        variant="outlined"
                        helperText={emailError?.message}
                        error={emailError !== undefined}
                        {...register("email", {
                          required: {
                            value: true,
                            message: "Campo obligatorio",
                          },
                          pattern: {
                            value: email_regex,
                            message: "Correo electrónico inválido",
                          },
                          validate: {
                            rangeLength: (value) =>
                              value.split("@")[0].length <= 64 ||
                              "Correo electrónico inválido",
                          },
                        })}
                      />
                    </Grid>
                  </Grid>
                  <Grid
                    item
                    container
                    direction="row"
                    xs={8}
                    sm={8}
                    md={4}
                    justifyContent="center"
                  >
                    <Grid item container justifyContent="center">
                      <PrimaryButton
                        width="373px"
                        height="53px"
                        action={() => {}}
                        text="Recuperar Contraseña"
                        fontSize="20"
                        type="submit"
                        cs={{
                          borderRadius: "25px",
                        }}
                      ></PrimaryButton>
                    </Grid>
                  </Grid>
                </Grid>
              </form>
            ) : (
              <Grid
                item
                container
                direction="column"
                alignItems="center"
                spacing={6}
                width={"90%"}
              >
                <Grid item>
                  <Typography
                    style={{
                      fontFamily: "Inter",
                      fontSize: "16px",
                      lineHeight: "24px",
                      letterSpacing: "0.15px",
                      textAlign: "center",
                      marginBottom: "2px",
                      color: "#FFFFFF",
                      fontWeight: "400",
                    }}
                  >
                    Te hemos enviado un correo electrónico a <br />{" "}
                    <b>{userEmail}</b> con los pasos a seguir
                    <br /> para restablecer tu contraseña.
                  </Typography>
                </Grid>
                <Grid
                  item
                  sx={{ width: "100%" }}
                  container
                  justifyContent="center"
                >
                  <PrimaryButton
                    width="373px"
                    height="53px"
                    action={() => {
                      navigate("/login")
                    }}
                    text="Aceptar"
                    fontSize="20"
                    cs={{
                      borderRadius: "25px",
                    }}
                  ></PrimaryButton>
                </Grid>
              </Grid>
            )}
          </Grid>
        </div>
      </Grid>
      <Grid item marginTop={recoverSuccess ? "125px" : "45px"}>
        <img
          alt="andromeda"
          src={monumentalLogo}
          className={classes.monumentalLogo}
        />
      </Grid>
    </Box>
  )
}

export default RecoverPassword
