import { Typography } from "@mui/material"
import { makeStyles } from "@mui/styles"
import PropTypes from "prop-types"
import React from "react"

const useStyles = makeStyles(() => ({
  fileInputWrapper: {
    position: "relative",
    width: "235px",
    height: "127px",
    border: "1px solid #000000",
    borderRadius: "10px",
    cursor: "pointer",
    overflow: "hidden",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#e5e5e5",
    "&:hover": {
      backgroundColor: "#989898",
    },
  },
  InputWrapperError: {
    borderColor: "red !important",
  },
  fileInputHidden: {
    position: "absolute",
    width: "100%",
    height: "100%",
    opacity: 0,
    cursor: "pointer",
  },
  fileInputContent: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    textAlign: "center",
    padding: "10px",
    fontFamily: "Inter",
    fontSize: "14px",
    color: "#666",
    pointerEvents: "none",
  },
  imagePreview: {
    maxWidth: "40px",
    maxHeight: "40px",
    objectFit: "contain",
    pointerEvents: "none",
  },
}))

const FileInputManagment = ({
  parameters,
  setParameters,
  errors,
  setErrors,
}) => {
  const classes = useStyles()

  const handleSeparatorChange = (event) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      SeparatorError: "",
    }))
    const file = event.target.files[0]

    if (!file) return

    const allowedTypes = ["image/png", "image/svg+xml", "image/webp"]
    if (!allowedTypes.includes(file.type)) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SeparatorError: "Formato de imagen no válido",
      }))
      return
    }

    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SeparatorError: "Tamaño de imagen demasiado grande",
      }))
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const base64 = e.target.result

      const img = new Image()
      img.onload = () => {
        img.onerror = () => {
          setErrors((prevErrors) => ({
            ...prevErrors,
            SeparatorError:
              "Error cargando imagen. Por favor, intenta con otro archivo.",
          }))
          return
        }
        let resizedWidth = img.width
        let resizedHeight = img.height
        if (img.width > 70) {
          resizedWidth = 70
          resizedHeight = (img.height / img.width) * 70
        }
        if (resizedHeight > 70) {
          resizedHeight = 70
          resizedWidth = (img.width / img.height) * 70
        }
        if (img.width < 40) {
          resizedWidth = 40
          resizedHeight = (img.height / img.width) * 40
        }
        if (resizedHeight < 40) {
          resizedHeight = 40
          resizedWidth = (img.width / img.height) * 40
        }

        setParameters((prevParameters) => ({
          ...prevParameters,
          separator: base64,
          separatorWidth: Math.round(resizedWidth),
          separatorHeight: Math.round(resizedHeight),
        }))
      }
      img.src = base64
    }
    reader.onerror = () => {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SeparatorError:
          "Error leyendo el archivo. Por favor, intenta de nuevo.",
      }))
      return
    }
    reader.readAsDataURL(file)
  }
  return (
    <React.Fragment>
      <div
        className={
          classes.fileInputWrapper +
          " " +
          (errors.SeparatorError != "" ? classes.InputWrapperError : "")
        }
      >
        <input
          type="file"
          accept="image/png, image/svg+xml, image/webp"
          name="separator"
          id="separator"
          className={classes.fileInputHidden}
          onChange={handleSeparatorChange}
        />
        <div className={classes.fileInputContent}>
          <div>
            {parameters.separator ? (
              <img
                src={parameters.separator}
                alt="Separator preview"
                className={classes.imagePreview}
              />
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="25"
                height="25"
                viewBox="0 0 25 25"
                fill="none"
              >
                <path
                  d="M3.45833 24.125C2.74792 24.125 2.13976 23.872 1.63385 23.3661C1.12795 22.8602 0.875 22.2521 0.875 21.5417V3.45833C0.875 2.74792 1.12795 2.13976 1.63385 1.63385C2.13976 1.12795 2.74792 0.875 3.45833 0.875H13.7917V3.45833H3.45833V21.5417H21.5417V11.2083H24.125V21.5417C24.125 22.2521 23.872 22.8602 23.3661 23.3661C22.8602 23.872 22.2521 24.125 21.5417 24.125H3.45833ZM4.75 18.9583H20.25L15.4062 12.5L11.5312 17.6667L8.625 13.7917L4.75 18.9583ZM18.9583 8.625V6.04167H16.375V3.45833H18.9583V0.875H21.5417V3.45833H24.125V6.04167H21.5417V8.625H18.9583Z"
                  fill="#1C1B1F"
                />
              </svg>
            )}
          </div>
          <div
            style={{
              fontSize: "12px",
              marginTop: "5px",
              fontFamily: "Inter",
              fontWeight: "500",
              color: "rgba(0, 0, 0, 0.50)",
              width: "135px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            Sube una imagen / icono .png .svg .webp
          </div>
          <div
            style={{
              fontSize: "10px",
              marginTop: "5px",
              fontFamily: "Inter",
              fontWeight: "400",
              color: "rgba(0, 0, 0, 0.40)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "235px",
            }}
          >
            Tamaño max 70x70px y 5mb.
          </div>
        </div>
      </div>
      {errors.SeparatorError == "" ? null : (
        <Typography
          sx={{
            fontSize: "12px",
            marginTop: "5px",
            fontFamily: "Inter",
            fontWeight: "600",
            color: "red",
          }}
        >
          {errors.SeparatorError}
        </Typography>
      )}
    </React.Fragment>
  )
}

FileInputManagment.propTypes = {
  parameters: PropTypes.object.isRequired,
  setParameters: PropTypes.func.isRequired,
  errors: PropTypes.object.isRequired,
  setErrors: PropTypes.func.isRequired,
}

export default FileInputManagment
