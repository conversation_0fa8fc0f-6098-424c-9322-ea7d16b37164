export const StatusPipe = (status) => {
  let newStatus = ""
  switch (status.status) {
    case "NEW":
      newStatus = "Nuevo"
      break
    case "APPROVED":
      newStatus = "Aprobado"
      break
    case "REJECTED":
      newStatus = "Rechazado"
      break
    case "INAPPROPIATE":
      newStatus = "Inapropiado"
      break
    case "DISCARDED":
      newStatus = "Descartado"
      break
    case "UNPUBLISED":
      newStatus = "Despublicado"
      break
  }
  return newStatus
}
