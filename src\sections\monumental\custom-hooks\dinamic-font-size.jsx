import { useState, useRef, useEffect } from 'react';

export const useDynamicFontSize = (lines = 3, minSize = 12, maxSize = 16) => {
  const ref = useRef(null);
  const [fontSize, setFontSize] = useState(maxSize);

  useEffect(() => {
    const adjustFontSize = () => {
      if (!ref.current) return;

      const element = ref.current;
      let currentSize = maxSize;
      element.style.fontSize = `${currentSize}px`;
      
      // Set line clamping
      element.style.display = '-webkit-box';
      element.style.webkitLineClamp = lines;
      element.style.webkitBoxOrient = 'vertical';
      element.style.overflow = 'hidden';
      
      // Check if text overflows
      const checkOverflow = () => {
        const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
        const maxHeight = lineHeight * lines;
        return element.scrollHeight > maxHeight + 2; // Small buffer
      };

      // Reduce font size until it fits or reaches minimum
      while (checkOverflow() && currentSize > minSize) {
        currentSize -= 0.5;
        element.style.fontSize = `${currentSize}px`;
      }
      
      setFontSize(currentSize);
    };

    adjustFontSize();
    window.addEventListener('resize', adjustFontSize);
    return () => window.removeEventListener('resize', adjustFontSize);
  }, [lines, minSize, maxSize]);

  return { ref, fontSize };
};

