import React, { useState, useEffect, useRef, useCallback } from 'react';
import fondo from "../../../../assets/fondo-ganadores.png"; 
import tribu from "../../../../assets/tribu.png";
import corona from "../../../../assets/corona.png";
import trivia from "../../../../assets/TRIVIA.png";
import promo from "../../../../assets/promocion.png";
import simon from "../../../../assets/simon2.png";
import com from "../../../../assets/com.png";
import { useParams, useNavigate } from "react-router-dom";
import PropTypes from "prop-types"
import { completeTriviaById, getTriviaById, winnersTriviaById } from "../../../../sections/trivia/services/api"
import WinnersList from './WinnersList'; // Asegúrate de que la ruta sea correcta
import Loader from './Loader'
import { io } from 'socket.io-client';
import { TriviaSocketEvents } from '../../../trivia/socket-events/trivia-socket-events';
import {
  
  Grid,

} from "@mui/material"
const SOCKET_SERVER_URL = process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH; // La URL de tu backend NestJS (donde escucha el servidor de Nest)
const ASSETS_URL = process.env.REACT_APP_BACKEND_URL; // La URL de tus assets

const Final = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [campaign, setCampaign] = useState(null)
  const [winners, setWinners] = useState(null);


  function fetchWinners(id, navigate, winners) {
    setLoading(true);
      if (id) {
        try {

          winnersTriviaById(id).then(({ data }) => {
            console.log("Winners data:", data);
            setWinners(data);
            setLoading(false);
          });
          
        } catch (error) {
          console.error("Error fetching winners:", error);
        }
      } else {
        navigate("/home");
      }  
    }

  useEffect(() => {
    function fetchTriviaAndWinners() {
      getTriviaById(id)
        .then(({data}) => {
          setCampaign(data[0]);
          console.log("Campaign data:", data[0]);
          if (data[0].status === "FINISHED") {
            fetchWinners(id, navigate, data[0].winners);
          } 
        })
        .catch((error) => {
          console.error("Error fetching trivia data:", error);
          
        });
    }

    fetchTriviaAndWinners();
  }, [id, navigate]);

  useEffect(() => {
    const socket = io(SOCKET_SERVER_URL);

    socket.on(TriviaSocketEvents.COMPLETED_TRIVIA, (data) => {
      if(data.id !== id) return; // Ensure the data is for the current trivia
      console.log("Evento 'completed' recibido:", data);
      fetchWinners(id, navigate, data.winners);
    });

    return () => {

      socket.off(TriviaSocketEvents.COMPLETED_TRIVIA);
      socket.disconnect();
    };
  }, [id, navigate]);

  return (
    <React.Fragment>
      <div
        style={{
          width: "100vw",
          height: "100vh",
          zIndex: 5,
          backgroundImage: `url(${fondo})`,
          backgroundSize: "100% 100%",
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          justifyContent: "space-between",
          fontFamily: "Montserrat", // Cambiar la tipografía a Montserrat
          position: "relative", // Para superponer el negro transparente
        }}
      >
   {/*      <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(0, 0, 0, 0.77)", // Fondo negro transparente
            zIndex: -1, // Asegurar que esté detrás del contenido
          }}
        ></div> */}

        <div
          style={{
            width: "80vw",
            
            margin: "0 auto",
            marginTop:'3vh',
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 2, // Asegurar que el contenido esté encima del fondo negro
          }}
        >
          <div
            style={{
              color: "#FFF",
              textAlign: "center",
              fontFamily: "Montserrat",
              fontSize: "6vh",
              fontStyle: "normal",
              fontWeight: 900,
              lineHeight: "normal",
            }}
          >
            ¡Ranking!
          </div>
        {/*   <img
            src={tribu}
            style={{
             
              height: "14vh",
              zIndex: 2,
            }}
            alt=""
          /> */}
        </div>

        {/* Loader or Winners */}
        {loading ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "calc(100vh - 400px)",
              zIndex: 2, // Asegurar que el contenido esté encima del fondo negro
            }}
          >
            <Loader />
          </div>
        ) : (
          <WinnersList winners={winners} ranking={campaign.ranking} />
        )}

        {/* Footer */}
          <Grid
           item
          container
          direction="row"
          alignItems="center"
          style={{marginBottom:'2vh'}}
         
          >
              <Grid item xs={1} style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
      
          </Grid>
          <Grid item xs={2} style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
                     <img
            src={tribu}
            style={{
             
              height: "10vh",
              zIndex: 2,
            }}
            alt=""
          />
          </Grid>
           <Grid item xs={2} style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
                    {campaign && <img src={`${ASSETS_URL}/${campaign.logo_url}`} style={{ height: "9vh" }} alt="" />}  
          </Grid>
           <Grid item xs={2} style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
              <img src={simon}style={{height: "13vh",}} alt="" />

          </Grid>
        </Grid>
 {/*        <div
          style={{
            width: "100%",
            height: "15vh",
            display: "flex",
            

          }}
        >
          <div style={{width: "100%", height: "100%", display: "flex", justifyContent: "center",
          gap: "5vw",
            alignItems: "center"
          }}>
              <img
            src={tribu}
            style={{
             
              height: "10vh",
              zIndex: 2,
            }}
            alt=""
          />
          {campaign && <img src={`${ASSETS_URL}/${campaign.logo_url}`} style={{ height: "9vh" }} alt="" />}  

                <img src={simon}style={{height: "13vh",maxWidth:'20vw'}} alt="" />
            <div style={{width:'15vw'}}></div>
            <div></div>
          </div>
          
          
        </div> */}
      </div>
    </React.Fragment>
  );
};


export default Final;
