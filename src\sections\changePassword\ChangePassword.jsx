import { useState, useEffect } from "react"
import * as React from "react"
import { useApp } from "../../AppProvider"
import {
  Grid,
  Typography,
  Button,
  TextField,
  Box,
  InputAdornment,
  IconButton,
} from "@mui/material"
import { useSearchParams } from "react-router-dom"
import { changePassword, validateTokenExpiration } from "./services/api"
import { useNavigate } from "react-router"
import { makeStyles } from "@mui/styles"
import { useForm } from "react-hook-form"
import logoStock from "./../../assets/ConectiumFullLogo.svg"
import monumentalLogo from "./../../assets/monumentalLogo.png"
import { Visibility, VisibilityOff } from "@mui/icons-material"
import ExpiredLink from "../expiredLink"
import exclamationIcon from "./../../assets/exclamation.png"
import fondoLogin from "./../../assets/fondo-login-monumental.PNG"
import PrimaryButton from "../../components/PrimaryButton"

const useStyles = makeStyles(() => ({
  root: {
    height: "calc(100vh - 180px)",
    backgroundImage: `url(${fondoLogin})`,
    fontFamily: "Inter, sans-serif",
    padding: "90px 0",
    overflowY: "hidden",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
  loginIcon: {
    objectFit: "cover",
    width: "100%",
    height: "100%",
    background: "rgba(6 153 226)",
  },
  baseFrame: {
    height: "fit-content",
    width: "555px",
  },
  formInput: {
    "& .MuiOutlinedInput-root": {
      "& input:-webkit-autofill": {
        "-webkit-text-fill-color": "white",
        "-webkit-box-shadow": "0 0 0px 1000px transparent inset",
        transition: "background-color 5000s ease-in-out 0s",
      },
      "& input::placeholder": {
        color: "white",
        opacity: 1,
      },
      "& fieldset": {
        borderRadius: "8px",
        borderColor: "white",
      },
      "&.Mui-focused fieldset": {
        borderColor: "white",
      },
      "&.Mui-error fieldset": {
        borderColor: "white",
      },
    },
    "& .MuiInputBase-root": {
      height: "50px !important",
      width: "373px !important",
    },
    "& .MuiInputPlaceholder-root": {
      color: "white",
      fontFamily: "Inter",
      fontSize: "16px !Important",
      fontWeight: "500",
    },
    "& .Mui-focused": {
      color: "white !important",
    },
    "& .Mui-error": {
      color: "#d32f2f !important",
    },
  },
  phoneImage: {
    position: "absolute",
    height: "80%",
    marginRight: "8%",
  },
  titleText: {
    color: "#F5F5F5",
    margin: 0,
    fontWeight: "400",
    fontFamily: "Inter, sans-serif",
    fontSize: "35px",
    "& b": {
      color: "#009EFF",
      fontWeight: "700",
    },
    "& strong": {
      color: "#F5F5F5",
      margin: 0,
      fontWeight: "700",
      fontFamily: "Inter, sans-serif",
      fontSize: "35px",
    },
    ["@media (max-width: 1370px)"]: {
      fontSize: 30,
    },
  },
  titleImage: {
    width: "35vw",
    position: "absolute",
    bottom: "10vh",
    right: "45.9%",
  },

  esmsLogo: {
    width: "320px",
    marginBottom: "40px !important",
    ["@media (max-width: 1300px)"]: {
      width: "15vw",
    },
  },
  acceptButton: {
    borderColor: "#4E5589 !important",
    textTransform: "none !important",
    borderWidth: "2px !important",
    borderRadius: "8px !important",
    color: "#4E5589 !important",
    height: "53px !important",
    "&:hover": {
      /* background: " linear-gradient( #008C0D 33.74%, #00BF19 96.06%);", */
      background: "#4E5589 !important",
      boxShadow:
        "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
      color: "white !important",
    },
  },
  formFrame: {
    background: "rgba(231, 231, 231, 0.20)",
    borderRadius: "20px",
    height: "fit-content",
    width: "555px",
    display: "flex",
    padding: "36px 0px 80px 0",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: "60px",
  },
  monumentalLogo: {
    width: "221px",
    height: "72.28px",
  },
}))

const ChangePassword = () => {
  const navigate = useNavigate()
  let [searchParams] = useSearchParams()
  const classes = useStyles()
  const { showNotification } = useApp()
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    getValues,
  } = useForm()
  const { password: passwordError, secondPassword: secondPasswordError } =
    errors
  const [showPassword, setShowPassword] = useState(false)
  const [showRepeatPassword, setShowRepeatPassword] = useState(false)
  const [open, setOpen] = useState(false)
  const [expired, setExpired] = useState(false)

  const message =
    "El enlace que has recibido por correo electrónico ha caducado. Para recuperar tu contraseña, haz clic en el siguiente botón."

  useEffect(async () => {
    const token = searchParams.get("token")
    try {
      const response = await validateTokenExpiration(token)
      if (!response.data.isValid) {
        setOpen(true)
      } else {
        setExpired(true)
      }
    } catch (error) {
      setOpen(true)
    }
  }, [])

  const handleExpiredLinkButton = () => {
    navigate("/recover-password")
  }

  const onSubmit = async ({ password, secondPassword }) => {
    try {
      await changePassword(password, searchParams.get("token"))
      showNotification("Nueva contraseña registrada exitosamente", "success")
      setTimeout(() => {
        navigate("/login")
      }, 5000)
    } catch (error) {
      showNotification(
        "Lo sentimos, se ha producido un error inesperado al configurar la nueva contraseña",
        "error"
      )
    }
  }

  return (
    <React.Fragment>
      {expired == true ? (
        <Box className={classes.root}>
          <Grid
            item
            container
            className={classes.esmsLogo}
            justifyContent="center"
          >
            <img alt="andromeda" src={logoStock} style={{ height: "100%" }} />
          </Grid>

          <Grid
            item
            className={classes.baseFrame}
            xs={6.6}
            sm={6.6}
            md={6.6}
            container
            direction="column"
            alignItems="center"
          >
            <div className={classes.formFrame}>
              <Typography
                style={{
                  textAlign: "center",
                  marginBottom: "54px",
                  letterSpacing: "0.15px",
                  fontFamily: "Inter, sans-serif",
                  fontSize: "24px",
                  fontWeight: "600",
                  lineHeight: "24px",
                  padding: 0,
                  color: "#FFFFFF",
                }}
              >
                Nueva contraseña
              </Typography>
              <Grid item container direction="column" alignItems="center">
                <form
                  onSubmit={handleSubmit(onSubmit)}
                  style={{ width: "100%" }}
                >
                  <Grid
                    item
                    container
                    direction="column"
                    spacing={2}
                    alignItems="center"
                  >
                    <Grid
                      item
                      container
                      alignItems="flex-start"
                      justifyContent={"center"}
                      sx={{
                        width: "329px",
                        height: "72px",
                        marginLeft: "16px",
                        marginBottom: "20px",
                        padding: "5px 15px 5px 15px !important",
                        borderRadius: "8px",
                      }}
                    >
                      <Typography
                        style={{
                          fontFamily: "Inter, sans-serif",
                          fontSize: "14px",
                          lineHeight: "24px",
                          letterSpacing: "0.15px",
                          color: "#FFFFFF",
                          display: "flex",
                          fontWeight: "600",
                          alignItems: "center",
                          gap: "10px",
                          marginBottom: "5px",
                        }}
                      >
                        <img
                          src={exclamationIcon}
                          alt=""
                          width={"22px"}
                          height={"24px"}
                        />
                        Protege tu cuenta con:
                      </Typography>
                      <Typography
                        style={{
                          fontFamily: "Inter, sans-serif",
                          fontSize: "12px",
                          lineHeight: "20px",
                          fontWeight: "400",
                          letterSpacing: "0.15px",
                          color: "#FFFFFF",
                          textAlign: "center",
                        }}
                      >
                        Mínimo 6 dígitos. Mayúsculas. Minúsculas <br /> Números
                        y símbolos.
                      </Typography>
                    </Grid>
                    <Grid
                      item
                      container
                      direction="row"
                      justifyContent="center"
                      marginBottom="17px"
                    >
                      <Grid item container justifyContent="center">
                        <TextField
                          className={classes.formInput}
                          id="password"
                          placeholder="Nueva Contraseña"
                          type={showPassword === false ? "password" : "text"}
                          variant="outlined"
                          helperText={passwordError?.message}
                          error={passwordError !== undefined}
                          InputProps={{
                            style: {
                              textAlign: "center",
                              background: "transparent",
                            },
                            inputProps: {
                              style: {
                                textAlign: "center",
                                "&::placeholder": {
                                  color: "white",
                                },
                                color: "white",
                              },
                            },
                            endAdornment: (
                              <InputAdornment
                                position="end"
                                sx={{ left: "-15px" }}
                              >
                                <IconButton
                                  onClick={() => setShowPassword(!showPassword)}
                                  edge="end"
                                  sx={{
                                    padding: "8px",
                                    "&:hover": {
                                      background: "transparent",
                                    },
                                  }}
                                >
                                  {showPassword ? (
                                    <Visibility
                                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                    />
                                  ) : (
                                    <VisibilityOff
                                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                    />
                                  )}
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                          {...register("password", {
                            required: {
                              value: true,
                              message: "Campo obligatorio",
                            },
                            maxLength: {
                              value: 15,
                              message:
                                "La contraseña supera el límite de caracteres permitidos",
                            },
                            minLength: {
                              value: 6,
                              message:
                                "La contraseña no cumple con el mínimo de caracteres",
                            },
                          })}
                        />
                      </Grid>
                    </Grid>
                    <Grid
                      item
                      container
                      direction="row"
                      justifyContent="center"
                      marginBottom="60px"
                    >
                      <Grid item container justifyContent="center">
                        <TextField
                          className={classes.formInput}
                          id="s_password"
                          placeholder="Confirmar Contraseña"
                          type={
                            showRepeatPassword === false ? "password" : "text"
                          }
                          variant="outlined"
                          helperText={secondPasswordError?.message}
                          error={secondPasswordError !== undefined}
                          InputProps={{
                            style: {
                              textAlign: "center",
                              background: "transparent",
                            },
                            inputProps: {
                              style: {
                                textAlign: "center",
                                "&::placeholder": {
                                  color: "white",
                                },
                                color: "white",
                              },
                            },
                            endAdornment: (
                              <InputAdornment
                                position="end"
                                sx={{ left: "-15px" }}
                              >
                                <IconButton
                                  onClick={() =>
                                    setShowRepeatPassword(!showRepeatPassword)
                                  }
                                  edge="end"
                                  sx={{
                                    padding: "8px",
                                    "&:hover": {
                                      background: "transparent",
                                    },
                                  }}
                                >
                                  {showRepeatPassword ? (
                                    <Visibility
                                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                    />
                                  ) : (
                                    <VisibilityOff
                                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                    />
                                  )}
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                          {...register("secondPassword", {
                            required: {
                              value: true,
                              message: "Campo obligatorio",
                            },
                            validate: {
                              passwordsMatch: (value) =>
                                value === getValues("password") ||
                                "Las contraseñas no coinciden",
                            },
                            maxLength: {
                              value: 15,
                              message:
                                "La contraseña supera el límite de caracteres permitidos",
                            },
                            minLength: {
                              value: 6,
                              message:
                                "La contraseña no cumple con el mínimo de caracteres",
                            },
                          })}
                        />
                      </Grid>
                    </Grid>
                    <Grid
                      item
                      container
                      direction="row"
                      justifyContent="center"
                    >
                      <Grid item container justifyContent="center">
                        <PrimaryButton
                          width="328px"
                          height="53px"
                          action={() => {}}
                          text="Guardar"
                          fontSize="20"
                          type="submit"
                          cs={{
                            borderRadius: "25px",
                          }}
                        ></PrimaryButton>
                      </Grid>
                    </Grid>
                  </Grid>
                </form>
              </Grid>
            </div>
          </Grid>
          <Grid item marginTop={"0"}>
            <img
              alt="andromeda"
              src={monumentalLogo}
              className={classes.monumentalLogo}
            />
          </Grid>
        </Box>
      ) : (
        <React.Fragment></React.Fragment>
      )}
      <ExpiredLink
        open={open}
        setOpen={setOpen}
        message={message}
        button={handleExpiredLinkButton}
      />
    </React.Fragment>
  )
}

export default ChangePassword
