import React, { useState, useEffect } from "react"
import { <PERSON>ton, <PERSON>rid, Typography } from "@mui/material"
import PropTypes from "prop-types"
import { MonumentalMessagesSocketEvents } from "../../socket-events/monumental-socket-events"
import MessageCard from "../messageCard"
import { motion, AnimatePresence } from "framer-motion"
import { useDebouncedWindowSize } from "../../custom-hooks/debounced-window-size"
import { useGeneralMessageSocketManagement } from "../../custom-hooks/general-message-socket-management-hook"
import { messageVariants } from "../../animations/message-animation"


const TodayMessages = ({ socket, on, emit, off }) => {
  const showMoreMessagesBreakPoint = 1850

  const windowSize = useDebouncedWindowSize()

  const [messages, setMessages] = useState([])

  const {
    readyToSend,
    acceptAllTimeout,
    handleAproveAllMessages,
    handleApproveMessage,
    handleRejectMessage,
  } = useGeneralMessageSocketManagement(
    messages,
    setMessages,
    socket,
    emit,
    on,
    off,
    MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES,
    30,
    MonumentalMessagesSocketEvents.SEND_MESSAGES_TO_MODERATOR,
    25,
    20,
    MonumentalMessagesSocketEvents.MODERATOR_IS_FULL
  )

  useEffect(() => {
    if (!readyToSend) return
    emit(MonumentalMessagesSocketEvents.MODERATOR_REQUEST_MESSAGES, {
      quantity: 50,
    })
  }, [readyToSend])

  return (
    <React.Fragment>
      <Grid
        container
        direction="column"
        width={`${
          windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
        }px`}
      >
        <div
          style={{
            alignSelf: "flex-end",
            padding: "0 14px 58px 0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            gap: "13px",
          }}
        >
          <Typography
            sx={{
              color: "black",
              fontStyle: "normal",
              fontFamily: "Inter",
              fontSize: "13px",
              fontWeight: "500",
              padding: "0 !important",
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Aprobar todos los SMS (
            {windowSize.width >= showMoreMessagesBreakPoint
              ? messages.length > 12
                ? 12
                : messages.length
              : messages.length > 9
              ? 9
              : messages.length}
            )
          </Typography>
          <Button
            sx={{
              background: "#3DB76E",
              borderRadius: "8px",
              width: "127px",
              height: "36px",
              color: "#FFFF",
              fontStyle: "normal",
              fontFamily: "Inter",
              fontSize: "15px",
              fontWeight: "500",
              padding: "0 !important",
              fontFeatureSettings: "'liga' off, 'clig' off",
              textTransform: "none",
              "&:hover": {
                background: "#3DB75E",
              },
            }}
            disabled={messages.length == 0 || acceptAllTimeout}
            onClick={() =>
              handleAproveAllMessages(
                windowSize.width >= showMoreMessagesBreakPoint ? 12 : 9
              )
            }
          >
            Aprobar Todos
          </Button>
        </div>
        <Grid
          container
          direction="row"
          padding={"0 0 0 14px"}
          alignSelf={"flex-start"}
          gap={"17px 21px"}
          width={`${
            windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
          }px`}
        >
          {messages.length == 0 && (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                width: "100%",
              }}
            >
              <Typography
                sx={{
                  color: "#00182F",
                  fontStyle: "normal",
                  fontFamily: "Inter",
                  fontSize: "20px",
                  fontWeight: "400",
                  padding: "0 !important",
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                No hay resultados que mostrar
              </Typography>
            </div>
          )}
          <AnimatePresence>
            {messages
              .slice(0, windowSize.width >= showMoreMessagesBreakPoint ? 12 : 9)
              .map(({ msg_id, msg_date, msg_content }, ind) => {
                return (
                  <motion.div
                    key={msg_id}
                    variants={messageVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    layout // Enables smooth layout animations when items reorder
                    style={{ originX: 0 }} // For scale animation origin
                  >
                    <MessageCard
                      msg_id={msg_id}
                      msg_date={msg_date}
                      msg_content={msg_content}
                      handleApproveMessage={handleApproveMessage}
                      handleRejectMessage={handleRejectMessage}
                    />
                  </motion.div>
                )
              })}
          </AnimatePresence>
        </Grid>
      </Grid>
    </React.Fragment>
  )
}

TodayMessages.propTypes = {
  socket: PropTypes.object,
  on: PropTypes.func.isRequired,
  emit: PropTypes.func.isRequired,
  off: PropTypes.func.isRequired,
}

export default TodayMessages
