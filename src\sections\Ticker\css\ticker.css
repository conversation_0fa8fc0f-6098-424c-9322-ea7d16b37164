
.global-container {
  display: flex;
  width: 1920px;
  height: 100%;
  gap: 5px;
  flex-direction: column;
}

.container {
  display: flex;
  width: 1920px;
  height: fit-content;
  z-index: 10000;
}

.logo-container {
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ticker-container {
  overflow: hidden;
  box-sizing: border-box;
  height: 96px;
  display: flex;
  align-items: center;
  text-wrap: nowrap;
}

.ticker-wrap {
  position: relative;
  left: 100%;
}

.ticker {
  display: inline-block;
  white-space: nowrap;
}

.ticker-item {
  padding: 0 0 0 50px;
  font-size: 80px;
  line-height: 96px;
  font-weight: 600;
  width: fit-content;
  max-lines: 1;
  display: flex;
  gap: 50px;
}

.ticker-separator {
  width: fit-content;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shutdown-overlay {
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  background-color: var(--landing-bg, #FF0000);
  z-index: 10000;
}
