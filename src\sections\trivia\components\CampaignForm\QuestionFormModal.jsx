import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Checkbox,
  Typography,
} from '@mui/material';
import SecundaryButton from '../../../../components/SecundaryButton/SecundaryButton';
import PrimaryButton from '../../../../components/PrimaryButton/PrimaryButton';

const QuestionFormModal = ({ open, onClose, onSave, initialData, questionIndex }) => {
  const [formData, setFormData] = useState({
    question: '',
    answer: [
      { text: '', correct: false },
      { text: '', correct: false },
      { text: '', correct: false },
      { text: '', correct: false },
    ],
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (open && initialData) {
      setFormData(initialData);
    }
  }, [open, initialData]);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.question.trim()) {
      newErrors.question = 'La pregunta es requerida';
    }
    formData.answer.forEach((ans, index) => {
      if (!ans.text.trim()) {
        newErrors[`answer_${index}`] = `La respuesta ${index + 1} es requerida`;
      }
    });
    const hasCorrectAnswer = formData.answer.some((ans) => ans.correct);
    if (!hasCorrectAnswer) {
      newErrors.correctAnswer = 'Debe haber al menos una respuesta marcada como correcta';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field, value) => {
    const updatedFormData = Object.assign({}, formData);
    updatedFormData[field] = value;
    setFormData(updatedFormData);
    const updatedErrors = Object.assign({}, errors);
    delete updatedErrors[field]; // Limpiar error al cambiar el campo
    setErrors(updatedErrors);
  };

  const handleAnswerChange = (index, value) => {
    const updatedAnswers = formData.answer.map((ans, idx) => {
      if (idx === index) {
        return Object.assign({}, ans, { text: value });
      }
      return ans;
    });
    const updatedFormData = Object.assign({}, formData, { answer: updatedAnswers });
    setFormData(updatedFormData);
    const updatedErrors = Object.assign({}, errors);
    delete updatedErrors[`answer_${index}`]; // Limpiar error al cambiar la respuesta
    setErrors(updatedErrors);
  };

  const handleToggleCorrect = (index) => {
    const updatedAnswers = formData.answer.map((ans, idx) => {
      return Object.assign({}, ans, { correct: idx === index ? !ans.correct : false });
    });
    const updatedFormData = Object.assign({}, formData, { answer: updatedAnswers });
    setFormData(updatedFormData);

    const hasCorrectAnswer = updatedAnswers.some((ans) => ans.correct);

    // Limpiar el error de respuesta correcta si se selecciona una
    const updatedErrors = Object.assign({}, errors);
    if (hasCorrectAnswer) {
      delete updatedErrors.correctAnswer;
    } else {
      updatedErrors.correctAnswer = 'Debe haber al menos una respuesta marcada como correcta';
    }
    setErrors(updatedErrors);
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData, questionIndex);
      onClose();
    }
  };

  const handleClose = () => {
    setFormData({
      question: '',
      answer: [
        { text: '', correct: false },
        { text: '', correct: false },
        { text: '', correct: false },
        { text: '', correct: false },
      ],
    });
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Editar Pregunta  {questionIndex + 1}</DialogTitle>
      <DialogContent style={{ paddingTop: '10px' }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              label="Pregunta"
              fullWidth
              value={formData.question}
              onChange={(e) => handleChange('question', e.target.value)}
              error={!!errors.question}
              helperText={errors.question}
              inputProps={{ maxLength: 100 }}
            />
          </Grid>
          {formData.answer.map((ans, index) => (
            <Grid item xs={12} key={index} style={{ display: 'flex', alignItems: 'center' }}>
              <TextField
                label={`Respuesta ${index + 1}`}
                fullWidth
                value={ans.text}
                onChange={(e) => handleAnswerChange(index, e.target.value)}
                error={!!errors[`answer_${index}`]}
                helperText={errors[`answer_${index}`]}
                inputProps={{ maxLength: 50 }}
              />
              <Grid item>
                <Checkbox
                  checked={ans.correct}
                  onChange={() => handleToggleCorrect(index)}
                  color="primary"
                />
              </Grid>
            </Grid>
          ))}
          {errors.correctAnswer && Object.keys(errors).length === 1 && (
              <Typography color="error" style={{ marginTop: '10px', textAlign: 'center', width: '100%', fontSize:'12px' }}>
                {errors.correctAnswer}
              </Typography>
            )}
        </Grid>
      </DialogContent>
      <DialogActions style={{display: 'flex', gap: '15px',justifyContent: 'center', marginBottom: '20px'}}>
        <SecundaryButton 
        
          text={'Cancelar'}
          action={handleClose}
          width="125px"
          height="40px"
          fontSize="14"
          lineHeight="28"
          type="submit"
          noShadow={true}>
        </SecundaryButton>
        <PrimaryButton 
          text={'Guardar'}
          action={handleSave}
          width="125px"
          height="40px"
          fontSize="14"
          lineHeight="28"
          type="submit"
          noShadow={true}
          disabled={Object.keys(errors).length > 0}>
          
        </PrimaryButton>
      </DialogActions>
    </Dialog>
  );
};

QuestionFormModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  initialData: PropTypes.shape({
    question: PropTypes.string,
    answer: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string,
        correct: PropTypes.bool,
      })
    ),
  }),
  questionIndex: PropTypes.number,
};

export default QuestionFormModal;
