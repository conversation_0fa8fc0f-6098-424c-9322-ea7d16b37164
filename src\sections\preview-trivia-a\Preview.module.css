.violetCircle {
    width: 80vh;
    height: 80vh;
    background: #550DC5;
    position: absolute;
    z-index: 2;
    opacity: 0.45;
    border-radius: 430px;
    flex-shrink: 0;
    filter: blur(170px);
}
.yellowCircle {
     width: 80vh;
    height: 80vh;
    background: #FFD53D;
    position: absolute;
    z-index: 2;
    opacity: 0.4;

    border-radius: 430px;
    flex-shrink: 0;
    filter: blur(100px);
}
.pinkCircle {
    width: 60vh;
    height: 60vh;
    background: #DF08A8;
    position: absolute;
    z-index: 2;
    opacity: 0.25;
    border-radius: 430px;
    flex-shrink: 0;
    filter: blur(100px);
}


@keyframes moveDiagonalLeftUp {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(-100%, -100%);
  }
}

@keyframes moveDiagonalLeftDown {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(-100%, 100%);
  }
}

@keyframes moveDiagonalRightUp {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(150%, -40%);
  }
}

@keyframes moveDiagonalLeft {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(-200%, 30%);
  }
}

.violetCircle {
  animation: moveDiagonalRightUp 4s infinite;
}

.yellowCircle {
  animation: moveDiagonalRightUp 4s infinite;
}

.pinkCircle {
  animation: moveDiagonalLeft 4s infinite;
}