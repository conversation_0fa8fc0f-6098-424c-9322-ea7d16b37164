import { useEffect, useState } from "react"
import { debounce } from "lodash"

export const useDebouncedWindowSize = (delay = 250) => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  })

  useEffect(() => {
    const handleResize = debounce(() => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }, delay)

    window.addEventListener("resize", handleResize)
    handleResize() // Initial call

    return () => {
      handleResize.cancel() // Cancel debounce on unmount
      window.removeEventListener("resize", handleResize)
    }
  }, [delay])

  return windowSize
}