Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9070) msys-2.0.dll+0x1FE8E
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210286019, 0007FFFFA028, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA170  000210068E24 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA450  00021006A225 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDD8700000 ntdll.dll
7FFDD7A40000 KERNEL32.DLL
7FFDD5BD0000 KERNELBASE.dll
7FFDD64E0000 USER32.dll
7FFDD63D0000 win32u.dll
7FFDD64B0000 GDI32.dll
000210040000 msys-2.0.dll
7FFDD6110000 gdi32full.dll
7FFDD59A0000 msvcp_win.dll
7FFDD5850000 ucrtbase.dll
7FFDD6FC0000 advapi32.dll
7FFDD7B70000 msvcrt.dll
7FFDD7FD0000 sechost.dll
7FFDD8080000 RPCRT4.dll
7FFDD4E50000 CRYPTBASE.DLL
7FFDD6400000 bcryptPrimitives.dll
7FFDD7B10000 IMM32.DLL
