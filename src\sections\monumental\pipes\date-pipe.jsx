import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

export const DatePipe = ({ date, offset, formatString = 'dd/MM/yyyy - hh:mm a' }) => {
  try {
    // Convert offset to hours (e.g., -4 → -4 * 60 = -240 minutes)
    const offsetMinutes = offset * 60;
    
    // Create a timezone string like "Etc/GMT+4" for UTC-4
    const timeZone = `Etc/GMT${offset <= 0 ? '+' : '-'}${Math.abs(offset)}`;
    
    // Convert to zoned time
    const zonedDate = utcToZonedTime(new Date(date), timeZone);
    
    // Format with correct tokens
    return format(zonedDate, formatString);
  } catch (error) {
    console.error('Date formatting error:', error);
    return 'Invalid date';
  }
};