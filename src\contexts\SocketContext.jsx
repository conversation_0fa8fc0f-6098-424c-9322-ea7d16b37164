import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { TriviaSocketEvents } from '../sections/trivia/socket-events/trivia-socket-events';
import PropTypes from 'prop-types';

const SOCKET_SERVER_URL = typeof process !== 'undefined' && process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH ? process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH : 'http://localhost:3000';

const SocketContext = createContext(null);

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('Conectando...');

  useEffect(() => {
    const socketInstance = io(SOCKET_SERVER_URL);

    socketInstance.on('connect', () => {
      console.log('Conectado al servidor WebSocket con ID:', socketInstance.id);
      setConnectionStatus('Conectado');
    });

    socketInstance.on('disconnect', () => {
      console.log('Desconectado del servidor WebSocket');
      setConnectionStatus('Desconectado');
    });

    socketInstance.on('connect_error', (error) => {
      console.error('Error de conexión:', error.message);
      setConnectionStatus(`Error de conexión: ${error.message}`);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  return (
    <SocketContext.Provider value={{ socket, connectionStatus }}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = () => {
  return useContext(SocketContext);
};

SocketProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
