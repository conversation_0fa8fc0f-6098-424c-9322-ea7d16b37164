import { useEffect, useRef, useState, useCallback, useMemo } from "react"
import { io, Socket } from "socket.io-client"

export const useSocket = (url, options) => {
  const socketRef = useRef(null)
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    const socket = io(url, {
      ...options
    })

    socketRef.current = socket

    socket
      .on("connect_error", (error) => {
        console.error("❌ Error de conexión:", error.message)
        console.error("Detalles técnicos:", error)
        setIsConnected(false)
      })
      .on("error", (error) => {
        console.error("‼️ Error general en el socket:", error)
      })

    setIsConnected(true)

    return () => {
      socketRef.current.disconnect()
    }

  }, [])

  const emit = useCallback((event, ...args) => {
    if (socketRef.current) {
      socketRef.current.emit(event, ...args)
    } else {
      console.error("Cannot emit - socket not connected")
    }
  }, [])

  const on = useCallback((event, callback) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback)

      return () => {
        socketRef.current?.off(event, callback)
      }
    }
  }, [])

  const off = useCallback((event, callback) => {
    socketRef.current?.off(event, callback)
  }, [])

  return useMemo(
    () => ({
      socket: socketRef.current,
      emit,
      on,
      off,
      connected: isConnected,
    }),
    [emit, on, off, isConnected]
  )
}
