import { makeStyles } from "@mui/styles";

export const sectionHeaderStyles = makeStyles(() => ({
    container: {
        marginBottom: "78px",
        "@media screen and (max-width: 1350px)": {
            marginBottom: "70px",
          }
    },

    sectionTitle: {
        marginLeft: 5,
        fontFamily: "Inter !important",
        fontStyle: "normal !important",
        fontWeight: "700 !important",
        fontSize: "25px !important",
        lineHeight: "24px",
        letterSpacing: "0.15px",
        color: "#363636",
        marginBottom: "40px",
        
    },
    sectionSubTitle: {
        fontSize:'13px !important',
        padding:'16px 0px 0px 0px',
        fontFamily: "Inter !important",
        color:'#363636',
        margin:'0px !important',
        "& p":{
            margin:0
        }
    },
    sectionIconButton: {
        marginRight:'15px'
    },
    sectionIcon: {
        fontSize:'50px !important',
        color:'#009EFF',
        "@media screen and (max-width: 1350px)": {
            fontSize: "40px !important",
          }
    },
    

    
}));