stages:
  - build

build:
  stage: build
  before_script:
    - echo "Init Pipeline"
    - echo $CI_COMMIT_REF_NAME
  script:
    - pwd
    - cd
    - pwd
    - git clone https://$GIT_USER:$GIT_PASS@$GITLAB_URL
    - ls
    - cp /home/<USER>/deploys_cf/deploy-esms.prod.sh frontend-gateway-sms/
    - cp /home/<USER>/deploys_cf/deploy-esms-prod frontend-gateway-sms/
    - cp /home/<USER>/deploys_cf/.env.esmsfront frontend-gateway-sms/.env
    - cd frontend-gateway-sms
    - git checkout -f $CI_COMMIT_REF_NAME
    - git branch
    - git status
    - node -v 
    - nvm use 16.18.1
    - npm install
    - ls
    - echo "node modules installed"
    - echo "Starting build"
    - npm run build
    - ls
    - echo "Build ended"
    - echo "Push to cloudfront"
    - sh deploy-esms.prod.sh
    - echo "end of the pipeline"
  after_script:
    - cd
    - rm -r frontend-gateway-sms -f
    - rm -rf /home/<USER>/builds/iyY_xPzKg/0/cntm/frontend-gateway-sms
    - rm -rf /home/<USER>/builds/iyY_xPzKg/0/cntm/frontend-gateway-sms.tmp
  only:
    - master
