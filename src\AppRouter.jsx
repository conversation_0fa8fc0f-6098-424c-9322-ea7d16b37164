import React, { useEffect } from "react"
import {
  <PERSON>rows<PERSON><PERSON>outer,
  Routes,
  Route,
  useNavigate,
  useLocation,
} from "react-router-dom"
import { useApp } from "./AppProvider"
import { ROLES } from "./sections/sidebar/constants"
import MiniDrawer from "./sections/sidebar"
import Login from "./sections/login"
import Preview from "./sections/preview-trivia/Preview"
import RecoverPassword from "./sections/recoverPassword"
import ChangePassword from "./sections/changePassword"
import { ROUTES } from "./routers/constants"
import CreatePassword from "./sections/createPassword"
import NotificationSnackbar from "./components/notification-snackbar"
import { SECTIONS } from "./sections/sidebar/constants"
import Final from "./sections/preview-trivia/components/final"
import Ticker from "./sections/Ticker/Ticker"

const Redirect1 = () => {
  const { currentUser } = useApp()
  const navigate = useNavigate()
  const location = useLocation()
  useEffect(() => {
    if (currentUser) {
      navigate("/monumental")
    } else {
      navigate("/login")
    }
  }, [])
  return null
}

const Redirect2 = () => {
  const navigate = useNavigate()
  useEffect(() => {
    if (
      window.location.pathname.includes("/create-password") ||
      window.location.pathname.includes("/recover-password") ||
      window.location.pathname.includes("/change-password") ||
      window.location.pathname.endsWith("/cintillo")
    ){
      console.log("Redirect2")
      return
    }
    navigate("/")
  }, [])
  return null
}

const AppRouter = () => {
  const {
    currentUser,
    shouldShowNotification,
    hideNotification,
    notificationMessage,
    notificationSeverity,
    notificationOrigin,
    notificationMargin,
  } = useApp()

  const [sections, setSections] = React.useState(SECTIONS)

  return (
    <BrowserRouter>
      <NotificationSnackbar
        open={shouldShowNotification}
        setOpen={(open) => hideNotification()}
        message={notificationMessage}
        type={notificationSeverity}
        asOrigin={notificationOrigin}
        asMargin={notificationMargin}
      />
      <Routes>
        <Route exact path="/login" element={<Login />} />
        <Route exact path="/preview/:id" element={<Preview />} />
{/*         <Route exact path="/preview/:id/game" element={<Preview />} /> */}
        <Route exact path="/preview/:id/final" element={<Final />} />
        <Route path="/recover-password" element={<RecoverPassword />} exact />
        <Route path="/change-password" element={<ChangePassword />} exact />
        <Route path="/create-password" element={<CreatePassword />} />
        <Route path="/cintillo" element={<Ticker tickerCS={{position: "absolute", top: "0px", left: "0px", width: "100%"}} solo/>} />
        <Route element={<MiniDrawer user={currentUser} SECTIONS={sections} />}>
          {currentUser && ROUTES[currentUser.role]}
        </Route>
        <Route exact path="/" element={<Redirect1 />} />
        <Route path="*" element={<Redirect2 />} />
      </Routes>
    </BrowserRouter>
  )
}

export default AppRouter
