import React from "react";
import fondo from "../../assets/fondo-inicio.jpg";
import tribu from "../../assets/tribu.png";
import trivia from "../../assets/TRIVIA.png";
import simon from "../../assets/simon2.png";
import com from "../../assets/com.png";
import { useState , useEffect} from 'react';
import { getTriviaById }  from "../../sections/trivia/services/api";
import { useLocation ,useNavigate} from "react-router";
import { useSocket } from "../../custom-hooks/socket-hook"
import {TriviaSocketEvents } from "../../sections/trivia/socket-events/trivia-socket-events";
import { io } from 'socket.io-client';
import { ca } from "date-fns/locale";
import Game from "./components/game/Game";
import { useParams } from "react-router-dom";
import { SocketProvider } from '../../contexts/SocketContext';

const SOCKET_SERVER_URL = process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH; // La URL de tu backend NestJS (donde escucha el servidor de Nest)
const ASSETS_URL = process.env.REACT_APP_BACKEND_URL; // La URL de tus assets
const SHORT_URL = 'slices.vip/GP5sB/'; // La URL de tus assets
const SHORT_URL_AQ = 'slices.vip/MPHFH/'; // La URL de tus assets

const Preview = () => {

    const { id } = useParams();
  const location = useLocation()
 const navigate = useNavigate();
  const [campaign, setCampaign] = useState(null)
  const [loading, setLoading] = useState(true)

  const [dataReceived, setDataReceived] = useState([]);
  const [messageToSend, setMessageToSend] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('Conectando...');
  const [lastGlobalMessage, setLastGlobalMessage] = useState('');
  const [GameStarted, setGameStarted] = useState(false);

  useEffect(() => {

    const socket = io(SOCKET_SERVER_URL);

    // 2. Manejar eventos de conexión
    socket.on('connect', () => {
      console.log('Conectado al servidor WebSocket con ID:', socket.id);
      setConnectionStatus('Conectado');
    });


    socket.on('disconnect', () => {
      console.log('Desconectado del servidor WebSocket');
      setConnectionStatus('Desconectado');
    });

    socket.on('connect_error', (error) => {
      console.log('Error de conexión:',  process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH);
      console.error('Error de conexión:', error.message);
      setConnectionStatus(`Error de conexión: ${error.message}`);
    });

    // 3. Escuchar el evento 'newData' emitido desde NestJS
    socket.on(TriviaSocketEvents.START_TRIVIA, (data) => {
      if(data.id === id){
         setGameStarted(true);  
      }

    });


    // 6. Limpiar listeners y desconectar cuando el componente se desmonta
    return () => {
      socket.off('connect');
      socket.off('connected');
      socket.off('disconnect');
      socket.off('connect_error');
      socket.off(TriviaSocketEvents.BEGIN_TRIVIA);
      socket.disconnect();
    };
  }, []); 

  useEffect(() => {
    async function fetchCampaign() {
      setLoading(true);
      console.log("Fetching campaign with ID:", ASSETS_URL);

      if (id) {
        try {
          const { data } = await getTriviaById(id);
          console.log("Campaign data:", data[0]);
          setCampaign(data[0]);
          if (data[0].status !== "CREATED") {
            setGameStarted(true);
          } 
          if (data[0].status === "FINISHED") {
            navigate(`/preview/${id}/final/` );
          }
        } catch (error) {
          console.error("Error fetching campaign:", error);
        }
      } else {
        navigate("/home");
      }

      setLoading(false);
    }

    fetchCampaign();
  }, [id, navigate]);
  
  return (
    <SocketProvider>
      <React.Fragment>
        {!GameStarted ? (
          <div
            style={{
              backgroundImage: `url(${fondo})`,
              backgroundSize: "100% 100%",
              width: "100vw",
              height: "100vh",
              zIndex: -2,
              overflow: "hidden",
      
            }}
          >
    
            <div
              style={{
                display: "flex",
                flexDirection:'column',
                position: "relative",
                alignItems: "center",
                margin: "0 auto",
                width: "70vw",
                height: "100%",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "baseline",
                  justifyContent: "space-between", // Distribuir las imágenes con espacio entre ellas
                  marginTop: "5vh",
                  width: "100%",
                  fontFamily: "Montserrat", // Cambiar la tipografía a Montserrat
                }}
              >
                <img src={tribu} style={{ height: "13vh" }} alt="" />
              {campaign?.logo_url && <img src={`${ASSETS_URL}/${campaign.logo_url}`} style={{ height: "9vh" }} alt="" />}  
              <img src={simon}style={{height: "13vh",}} alt="" />
            
              </div>
              <div
                style={{
                  marginTop: "10vh",
                  display: "flex",
                  alignItems: "center",
                  width: "100%",
                  justifyContent: "space-between",
                  fontFamily: "Montserrat", // Cambiar la tipografía a Montserrat
                }}
              >
                <div style={{display: "flex", flexDirection: "column", width: "50%"}}>
                   <div
                  style={{
                    
                    marginBottom: "4vh",
                    color: "#FFF",
                    fontFamily: "Montserrat", // Cambiar la tipografía a Montserrat
                    fontSize: `6vh`, // Ajuste dinámico basado en el tamaño de la pantalla
                    minFontSize: "70px", // Mínimo de 70px
                    fontStyle: "normal",
                    fontWeight: 700,
                    lineHeight: "calc(3vw + 15px)",
                  }}
                >
                  Escanea el QR y empieza a jugar nuestra
                </div>
                <img src={trivia} style={{width:'95%', maxWidth:'90vh',}} alt="" />
                 <div style={{
                color: '#FFF',
                marginTop: '2vh',
                fontFamily: 'Montserrat',
                fontSize: '6vh',
                fontStyle: 'normal',
                fontWeight: 700,
                lineHeight: 'normal',
              }}  >{campaign?.title}</div>
                
                </div>
                   
                
                {campaign ? (
              <div style={{
                display: 'flex',
                marginRight:'1.5vw',
                flexDirection: 'column',
                justifyContent: 'center',alignItems: 'center',}}>   
                <div style={{borderRadius: "30px" , padding: '2vh',
                 backgroundColor: 'white'}}>
                  <img
                    style={{ height:'45vh', }}
                    src={campaign ? campaign.qrCode : ""}
                    alt="QR Code"
                  />
                
                  </div>
                  <div style={{color: 'white', fontSize: '2.5vh', textAlign: 'center', marginTop: '1.5vh'}}>
                    o ingresa en <b> {SHORT_URL + campaign.hash} </b>
                  </div>  
                </div>
                ) : (
                  <React.Fragment />
                )}
              </div>
            </div>

  
          </div>
        ) : (
          <Game campaign={campaign} />
        )}
      </React.Fragment>
    </SocketProvider>
  );
};

export default Preview;
