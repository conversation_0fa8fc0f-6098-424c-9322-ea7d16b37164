import React, { useState, useEffect, useRef, useCallback } from 'react';
import fondo from "../../../../assets/fondo.png"; 
import sideFondo from "../../../../assets/side-fondo.png"; 
import sideRFondo from "../../../../assets/fondo-side-r.png"; 
import tribu from "../../../../assets/logoAqustico.png";
import corona from "../../../../assets/corona.png";
import trivia from "../../../../assets/TRIVIA.png";
import promo from "../../../../assets/promocion.png";
import simon from "../../../../assets/simon2.png";
import com from "../../../../assets/com.png";
import { useParams, useNavigate } from "react-router-dom";
import PropTypes from "prop-types"
import { completeTriviaById, getTriviaById, winnersTriviaById } from "../../../../sections/trivia/services/api"
import WinnersList from './WinnersList'; // Asegúrate de que la ruta sea correcta
import Loader from './Loader'
import { io } from 'socket.io-client';
import { TriviaSocketEvents } from '../../../trivia/socket-events/trivia-socket-events';
import {
  
  Grid,

} from "@mui/material"
import styles from './Final.module.css';
const SOCKET_SERVER_URL = process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH; // La URL de tu backend NestJS (donde escucha el servidor de Nest)
const ASSETS_URL = process.env.REACT_APP_BACKEND_URL; // La URL de tus assets

const FinalAQ = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [campaign, setCampaign] = useState(null)
  const [winners, setWinners] = useState(null);


  function fetchWinners(id, navigate, winners) {
    setLoading(true);
      if (id) {
        try {

          winnersTriviaById(id).then(({ data }) => {
            console.log("Winners data:", data);
            setWinners(data);
            setLoading(false);
          });
          
        } catch (error) {
          console.error("Error fetching winners:", error);
        }
      } else {
        navigate("/home");
      }  
    }

  useEffect(() => {
    function fetchTriviaAndWinners() {
      getTriviaById(id)
        .then(({data}) => {
          setCampaign(data[0]);
          console.log("Campaign data:", data[0]);
          if (data[0].status === "FINISHED") {
            fetchWinners(id, navigate, data[0].winners);
          } 
        })
        .catch((error) => {
          console.error("Error fetching trivia data:", error);
          
        });
    }

    fetchTriviaAndWinners();
  }, [id, navigate]);

  useEffect(() => {
    const socket = io(SOCKET_SERVER_URL);

    socket.on(TriviaSocketEvents.COMPLETED_TRIVIA, (data) => {
      if(data.id !== id) return; // Ensure the data is for the current trivia
      console.log("Evento 'completed' recibido:", data);
      fetchWinners(id, navigate, data.winners);
    });

    return () => {

      socket.off(TriviaSocketEvents.COMPLETED_TRIVIA);
      socket.disconnect();
    };
  }, [id, navigate]);

  return (
    <React.Fragment>
      <div
        style={{
          width: "100vw",
          height: "100vh",
          zIndex: 5,
          backgroundImage: `url(${fondo})`,
          backgroundSize: "100% 100%",
          display: "flex",
          overflow: "hidden",
          alignItems: "center",
          flexDirection: "column",
          justifyContent: "space-between",
          fontFamily: "Montserrat", // Cambiar la tipografía a Montserrat
          position: "relative", // Para superponer el negro transparente
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundImage:`url(${sideFondo}), url(${sideRFondo})`,
            backgroundRepeat: "repeat-y, repeat-y",
            backgroundPosition: "left top, right top",
            backgroundSize: "4% 100%",
            backgroundColor: "rgba(0, 0, 0, 0.77)", // Fondo negro transparente
            zIndex: -1, // Asegurar que esté detrás del contenido
          }}
        ></div>
          <div  className={styles.violetCircle} style={{top: '-30%', left: '0%', zIndex:-1}}></div>
            <div className={styles.yellowCircle} style={{bottom: '-30%', left: '0%', zIndex:-1}}></div>
            <div className={styles.pinkCircle}  style={{bottom: '10%', right:'-10%', zIndex:-1}}></div>
        <div
          style={{
            width: "80vw",
            
            margin: "0 auto",
            marginTop:'3vh',
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 2, // Asegurar que el contenido esté encima del fondo negro
          }}
        >
          <div
            style={{
              color: "#FFF",
              textAlign: "center",
              fontFamily: "Montserrat",
              fontSize: "6vh",
              fontStyle: "normal",
              fontWeight: 900,
              lineHeight: "normal",
              zIndex: 3
            }}
          >
           ¡Ranking!
          </div>
        {/*   <img
            src={tribu}
            style={{
             
              height: "14vh",
              zIndex: 2,
            }}
            alt=""
          /> */}
        </div>

        {/* Loader or Winners */}
        {loading ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "calc(100vh - 400px)",
              zIndex: 3, // Asegurar que el contenido esté encima del fondo negro
            }}
          >
            <Loader />
          </div>
        ) : (
          <WinnersList winners={winners} ranking={campaign.ranking} style={{ zIndex: 3}} />
        )}

        {/* Footer */}
          <Grid
           item
          container
          direction="row"
          alignItems="baseline"
          style={{marginBottom:'2vh', justifyContent: "center", zIndex: 3}}
         
          >
              
          <Grid item  style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
                     <img
            src={tribu}
            style={{
             
              height: "6vh",
              zIndex: 2,
            }}
            alt=""
          />
          </Grid>
          
           <Grid item  style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
              <img src={simon}style={{height: "12vh",}} alt="" />

          </Grid>
           <Grid item style={{ display: "flex",
                  justifyContent: "center", alignItems: "center" }}>
                    {campaign && <img src={`${ASSETS_URL}/${campaign.logo_url}`} style={{ height: "9vh" }} alt="" />}  
          </Grid>
        </Grid>
 {/*        <div
          style={{
            width: "100%",
            height: "15vh",
            display: "flex",
            

          }}
        >
          <div style={{width: "100%", height: "100%", display: "flex", justifyContent: "center",
          gap: "5vw",
            alignItems: "center"
          }}>
              <img
            src={tribu}
            style={{
             
              height: "10vh",
              zIndex: 2,
            }}
            alt=""
          />
          {campaign && <img src={`${ASSETS_URL}/${campaign.logo_url}`} style={{ height: "9vh" }} alt="" />}  

                <img src={simon}style={{height: "13vh",maxWidth:'20vw'}} alt="" />
            <div style={{width:'15vw'}}></div>
            <div></div>
          </div>
          
          
        </div> */}
      </div>
    </React.Fragment>
  );
};


export default FinalAQ;
