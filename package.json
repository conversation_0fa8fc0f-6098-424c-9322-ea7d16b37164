{"name": "frontend-gateway-sms", "version": "0.7.0", "private": true, "dependencies": {"@emotion/react": "11.10.5", "@emotion/styled": "11.10.5", "@fontsource/open-sans": "4.5.8", "@fontsource/public-sans": "5.0.3", "@mui/icons-material": "5.2.0", "@mui/material": "5.10.12", "@mui/styled-engine-sc": "5.1.0", "@mui/styles": "5.2.2", "@mui/x-date-pickers": "5.0.7", "@testing-library/jest-dom": "5.15.1", "@testing-library/react": "11.2.7", "@testing-library/user-event": "12.8.3", "amqplib": "^0.10.8", "axios": "0.24.0", "chart.js": "3.9.1", "chartjs-plugin-datalabels": "2.2.0", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.0", "dayjs": "1.11.6", "dotenv": "10.0.0", "file-saver": "2.0.5", "framer-motion": "^4.1.17", "friendly-challenge": "^0.9.19", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "moment": "2.29.4", "prop-types": "15.8.1", "react": "17.0.2", "react-alert": "7.0.3", "react-chartjs-2": "4.3.1", "react-dom": "17.0.2", "react-google-recaptcha": "^3.1.0", "react-hook-form": "7.21.0", "react-router-dom": "6.0.2", "react-scripts": "4.0.3", "react-ticker": "^1.3.2", "short-number": "1.0.7", "socket.io-client": "4.7.2", "styled-components": "5.3.3", "web-vitals": "1.1.2", "xlsx": "https://cdn.sheetjs.com/xlsx-0.19.3/xlsx-0.19.3.tgz", "xlsx-js-style": "1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "eslint": "7.32.0", "eslint-plugin-react": "7.27.1"}}