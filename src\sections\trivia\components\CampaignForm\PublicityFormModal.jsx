import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
} from '@mui/material';
import PrimaryButton from '../../../../components/PrimaryButton';
import SecundaryButton from '../../../../components/SecundaryButton/SecundaryButton';
import NumericInput from './NumericInput';

const assetsUrl = process.env.REACT_APP_BACKEND_URL || "http://localhost:7178"

const PublicityFormModal = ({ open, onClose, onSubmit , publicityData}) => {
  const [formData, setFormData] = useState({
    description: '',
    url_mob: null,
    url_web: null,
    duration: '',
  });
  const [previewMode, setPreviewMode] = useState('mobile');
  const [initialFormData, setInitialFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [mobPreview, setMobPreview] = useState(null);
  const [webPreview, setWebPreview] = useState(null);

  useEffect(() => {
    if(publicityData){
      if (typeof publicityData.url_mob === 'string') {
        setMobPreview(assetsUrl + '/' + publicityData.url_mob);
      }
      if (typeof publicityData.url_web === 'string') {
        setWebPreview(assetsUrl + '/' + publicityData.url_web);
      }
      setFormData({
        description: publicityData.description ? publicityData.description.replace(/<br>/g, '\n') : '',
        url_mob: publicityData.url_mob || null,
        url_web: publicityData.url_web || null,
        duration: publicityData.duration || '',
      });
    }
    if (open) {
      const updatedInitialFormData = Object.assign({}, formData);
      setInitialFormData(updatedInitialFormData);
    }
  }, [open]);

  const handleClose = () => {
    const restoredFormData = Object.assign({}, initialFormData);
    setFormData(restoredFormData);
    onClose();
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.description.trim()) {
      newErrors.description = 'La descripción es requerida';
    }
    if (!formData.duration || isNaN(formData.duration) || formData.duration < 5) {
      newErrors.duration = 'La duración debe ser un número mayor o igual a 5';
    }
    if (!formData.url_mob) {
      newErrors.url_mob = 'La imagen móvil es requerida';
    }
    if (!formData.url_web) {
      newErrors.url_web = 'La imagen web es requerida';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      const formattedFormData = Object.assign({}, formData);
      formattedFormData.description = formData.description.replace(/\n/g, '<br>');
      onSubmit(formattedFormData);
      onClose();
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedFormData = Object.assign({}, formData);
    updatedFormData[name] = value;
    setFormData(updatedFormData);
    const updatedErrors = Object.assign({}, errors);
    delete updatedErrors[name]; // Limpiar error al cambiar el valor
    setErrors(updatedErrors);
  };

  const handleFileChange = (e) => {
    const { name } = e.target;
    const file = e.target.files[0];
    const validExtensions = ["image/png", "image/jpeg", "image/jpg"];
    if (file && !validExtensions.includes(file.type)) {
      const updatedErrors = Object.assign({}, errors);
      updatedErrors[name] = 'El archivo debe ser PNG, JPG o JPEG';
      setErrors(updatedErrors);
      return;
    }
    if (name === 'url_mob') {
      setMobPreview(null);
    } else if (name === 'url_web') {
      setWebPreview(null);
    }
 
    const updatedFormData = Object.assign({}, formData);
    updatedFormData[name] = file;
    setFormData(updatedFormData);
    const updatedErrors = Object.assign({}, errors);
    delete updatedErrors[name]; // Limpiar error al cambiar el archivo
    setErrors(updatedErrors);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth style={{ minHeight: '500px' }}>
      <DialogTitle>
        <Grid container spacing={4}>
          <Grid item xs={6}>Publicidad</Grid>
          <Grid item xs={6}>Preview</Grid>
        </Grid>
      </DialogTitle>
      <DialogContent style={{ paddingTop: '10px', minHeight: '400px' }}>
        <Grid container spacing={4}>
          <Grid item xs={6}>
            <Grid container spacing={2} style={{ justifyContent: 'center' }}>
              <Grid item xs={12}>
                <TextField
                  name="description"
                  label="Descripción"
                  fullWidth
                  multiline
                  rows={5}
                  maxRows={5}
                  value={formData.description}
                  onChange={handleChange}
                  error={!!errors.description}
                  helperText={errors.description}
                />
              </Grid>
               <Grid item xs={12}>
                <TextField
                  name="duration"
                  label="Duración (segundos)"
                  type="number"
                  fullWidth
                  value={formData.duration}
                  onChange={handleChange}
                  error={!!errors.duration}
                  helperText={errors.duration}
                />
              </Grid>
              {previewMode === 'mobile' && (
                 <Grid item xs={8}>
                <Button
                  variant="contained"
                  component="label"
                  fullWidth
                  style={{ backgroundColor: '#009EFF', }}
                >
                  Subir Imagen Móvil
                  <input
                    type="file"
                    accept="image/*"
                    name="url_mob"
                    hidden
                    onChange={handleFileChange}
                  />
                </Button>
              
                 {errors.url_mob && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', textAlign: 'center' }}>
                    {errors.url_mob}
                  </div>
                )}
                 {errors.url_web && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', textAlign: 'center' }}>
                    {errors.url_web}
                  </div>
                )}
              </Grid>
              )}
              {previewMode === 'web' && (
              <Grid item xs={8}>
                <Button
                  variant="contained"
                  component="label"
                  style={{ backgroundColor: '#009EFF', }}
                  fullWidth
                >
                  Subir Imagen Web
                  <input
                    type="file"
                    name="url_web"
                    accept="image/*"
                    hidden
                    onChange={handleFileChange}
                  />
                </Button>
                {errors.url_web && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', textAlign: 'center' }}>
                    {errors.url_web}
                  </div>
                )}
                 {errors.url_mob && (
                  <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', textAlign: 'center' }}>
                    {errors.url_mob}
                  </div>
                )}
              </Grid>)}
            </Grid>
          </Grid>
          <Grid item xs={6}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Button
                  variant="contained"
                  style={{ backgroundColor: previewMode === 'mobile' ? '#009EFF' : 'transparent', color: previewMode === 'mobile' ? 'white' : '#009EFF', border:'1px solid #009EFF' }}
                  onClick={() => setPreviewMode('mobile')}
                  disabled={previewMode === 'mobile'}
                >
                  Móvil
                </Button>
                <Button
                  variant="contained"
                  style={{ backgroundColor: previewMode === 'web' ? '#009EFF' : 'transparent', color: previewMode === 'web' ? 'white' : '#009EFF', border:'1px solid #009EFF'  }}
                  onClick={() => setPreviewMode('web')}
                  disabled={previewMode === 'web'}
                >
                  Web
                </Button>
              </Grid>
              <Grid item xs={12}>
                <div style={{minHeight: '320px'}}>
                {previewMode === 'mobile' && formData.url_mob && (
                  
                  <Grid style={{ width: '180px', height: '320px', border: '1px solid #ccc', margin: '0 auto', overflow: 'hidden', position: 'relative' }}>
                    <div style={{ position: 'absolute', width: '100%', height: '100%', background: "#000000B2", zIndex: 1 }}></div>
                    <img
                      src={mobPreview ? mobPreview : URL.createObjectURL(formData.url_mob)}
                      alt="Previsualización Imagen Móvil"
                      style={{ width: '100%', height: '100%', border: '1px solid #ccc' }}
                    />
                    <div style={{ zIndex: 2, fontSize: '16px', display: 'flex', textAlign: 'center', flexDirection: 'column', color: 'white', width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center', position: 'absolute', top: '0%' }}>
                      <div dangerouslySetInnerHTML={{ __html: formData.description.replace(/\n/g, '<br>') }}></div>
                    </div>
                  </Grid>
                )}
                {previewMode === 'web' && formData.url_web && (
                  <Grid style={{ width: '100%', height: '320px', border: '1px solid #ccc', margin: '0 auto', overflow: 'hidden', position: 'relative' }}>
                    <div style={{ position: 'absolute', width: '100%', height: '100%', background: "#000000B2", zIndex: 1 }}></div>
                    <img
                      src={webPreview ? webPreview : URL.createObjectURL(formData.url_web)}
                      alt="Previsualización Imagen Web"
                      style={{ width: '100%', height: '100%', border: '1px solid #ccc' }}
                    />
                    <div style={{ zIndex: 2, fontSize: '16px', display: 'flex', textAlign: 'center', flexDirection: 'column', color: 'white', width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center', position: 'absolute', top: '0%' }}>
                      <div dangerouslySetInnerHTML={{ __html: formData.description.replace(/\n/g, '<br>') }}></div>
                    </div>
                  </Grid>
                )}
                </div>
                <div style={{width:'100%', textAlign:'center',fontSize:12, marginTop:"4px"}}>{previewMode === 'mobile' ? '414x896' : '1594x932'}</div>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginBottom: '20px' }}>
        <SecundaryButton 
          
          text={'Cancelar'}
          action={handleClose}
          width="125px"
          height="40px"
          fontSize="14"
          lineHeight="28"
          type="submit"
          noShadow={true}>
        </SecundaryButton>
        <PrimaryButton 
          text={'Guardar'}
          action={handleSubmit}
          width="125px"
          height="40px"
          fontSize="14"
          lineHeight="28"
          type="submit"
          noShadow={true}
          disabled={Object.keys(errors).length > 0}>
          
        </PrimaryButton>
      </DialogActions>
    </Dialog>
  );
};

PublicityFormModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  publicityData: PropTypes.object, // Datos de publicidad iniciales
};

export default PublicityFormModal;
