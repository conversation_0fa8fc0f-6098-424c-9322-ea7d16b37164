import React, { useEffect, useState } from "react"
import { usePagination } from "../../../../custom-hooks/pagination-hook"
import { useApp } from "../../../../AppProvider"
import { useNavigate } from "react-router-dom"
import { useDebouncedWindowSize } from "../../custom-hooks/debounced-window-size"
import { Button, Grid, Typography } from "@mui/material"
import MessageCard from "../messageCard"
import { motion, AnimatePresence } from "framer-motion"
import { messageVariants } from "../../animations/message-animation"
import moment from "moment"

const DiscardedMessages = () => {
  const showMoreMessagesBreakPoint = 1850

  const windowSize = useDebouncedWindowSize()
  const [order, setOrder] = useState("asc")
  const [orderBy, setOrderBy] = useState("messageDateLabel")
  const [page, setPage] = useState(0)
  const { showNotification } = useApp()
  const numberElements = windowSize.width >= showMoreMessagesBreakPoint ? 12 : 9

  const { data, totalData, loading } = usePagination(
    "/monumental-messages/getAll/DISCARDED",
    page + 1,
    numberElements,
    "",
    "messageLabel",
    order,
    orderBy,
    showNotification,
    (data) => data,
    "&fromDate=" + moment().toISOString() + "&toDate=" + moment().toISOString()
  )

  useEffect(() => {
    setPage(0)
  }, [numberElements])

  return (
    <React.Fragment>
      <Grid
        container
        direction="column"
        width={`${
          windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
        }px`}
      >
        <Grid
          container
          direction="row"
          padding={"0 0 0 14px"}
          alignSelf={"flex-start"}
          margin={"126px 0 0 0"}
          gap={"17px 21px"}
          width={`${
            windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
          }px`}
        >
          {data.length == 0 && (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                width: "100%",
              }}
            >
              <Typography
                sx={{
                  color: "#00182F",
                  fontStyle: "normal",
                  fontFamily: "Inter",
                  fontSize: "20px",
                  fontWeight: "400",
                  padding: "0 !important",
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                No hay resultados que mostrar
              </Typography>
            </div>
          )}
          <AnimatePresence>
            {data
              .slice(0, numberElements)
              .map(({ id, msg_date, content }, ind) => {
                return (
                  <motion.div
                    key={ind}
                    variants={messageVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    layout // Enables smooth layout animations when items reorder
                    style={{ originX: 0 }} // For scale animation origin
                  >
                    <MessageCard
                      msg_id={id}
                      msg_date={msg_date}
                      msg_content={content}
                      noActions={true}
                      handleApproveMessage={() => {}}
                      handleRejectMessage={() => {}}
                    />
                  </motion.div>
                )
              })}
          </AnimatePresence>
        </Grid>
        {data.length > 0 && (
          <Grid
            item
            container
            direction={"row"}
            margin={"40px 0 0 0"}
            width={`${
              windowSize.width >= showMoreMessagesBreakPoint ? 1695 : 1270
            }px`}
            height={"10px"}
            justifyContent={"end"}
            gap={"19px"}
          >
            <a
              style={{
                fontFamily: "Inter",
                fontSize: "13px",
                fontWeight: "500",
                letterSpacing: "0.15px",
                cursor: page == 0 ? "none" : "pointer",
                pointerEvents: page == 0 ? "none" : "auto",
                color: page == 0 ? "#E3E2E2" : "black",
              }}
              onClick={() => setPage((p) => p - 1)}
            >
              Anterior
            </a>
            <div
              style={{
                fontFamily: "Inter",
                fontSize: "13px",
                fontWeight: "500",
                gap: "18px",
                letterSpacing: "0.15px",
                cursor:
                  (numberElements) *
                    (page + 1) >=
                  totalData
                    ? "none"
                    : "pointer",
                pointerEvents:
                  (numberElements) *
                    (page + 1) >=
                  totalData
                    ? "none"
                    : "auto",
                color:
                  (numberElements) *
                    (page + 1) >=
                  totalData
                    ? "#E3E2E2"
                    : "black",
                display: "flex",
                alignItems: "center",
              }}
              onClick={() => setPage((p) => p + 1)}
            >
              Siguiente{" "}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="7"
                height="12"
                viewBox="0 0 7 12"
                fill="none"
              >
                <path
                  d="M7.09668 6.07797L6.51554 5.46952L1.2648 -2.5492e-07L-2.62792e-05 1.21689L4.66938 6.07813L-2.67042e-05 10.9394L1.2648 12.1563L6.51554 6.68673L7.09668 6.07797Z"
                  fill={
                    (numberElements) *
                      (page + 1) >=
                    totalData
                      ? "#E3E2E2"
                      : "black"
                  }
                />
              </svg>
            </div>
          </Grid>
        )}
      </Grid>
    </React.Fragment>
  )
}

export default DiscardedMessages
