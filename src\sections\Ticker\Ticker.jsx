import React, { useState, useEffect, useRef, useCallback } from "react"
import { MonumentalTickerSocketEvents } from "./socket-events/monumental-ticker-socket-event"
import "./css/ticker.css"
import { useSocket } from "../../custom-hooks/socket-hook"
import { useCheckFetchNewMessage } from "./custom-hooks/check-fetch-new-message-hook"
import { useCalculateAndSetAnimation } from "./custom-hooks/calculate-and-set-animation-hook"
import TribuLogo from "../../assets/tribu-deportiva.png"
import AqusticoLogo from "../../assets/aqustico.png"
import { useFetchTickerParameters } from "./custom-hooks/fetch-ticker-parameters-hook"
import { tickerModels } from "./enums/ticker-models"
import { useFetchTickerPublicity } from "./custom-hooks/fetch-ticker-publicity-hook"
import { useManageMessages } from "./custom-hooks/manage-messages-hook"
import PropTypes from "prop-types"

const DEFAULT_INITIAL_MESSAGE = {
  id: 0,
  message: "Bienvenidos",
  date: new Date(),
}

const Ticker = ({ tickerCS, solo = false }) => {
  const [messagesItems, setMessagesItems] = useState([DEFAULT_INITIAL_MESSAGE])

  const [fetchParameters, setFetchParameters] = useState(0)
  const [newMessageData, setNewMessageData] = useState(null)
  const { tickerParameters, setTickerParameters } =
    useFetchTickerParameters(fetchParameters)
  const [currentRepetitions, setCurrentRepetitions] = useState(0)
  const [currentRepetitionIndex, setCurrentRepetitionIndex] = useState(0)
  const [repetitionMessages, setRepetitionMessages] = useState([])
  const [repeat, setRepeat] = useState(0)

  const [fetchMessages, setFetchMessages] = useState(0)

  const { socket, on, emit, connected, off } = useSocket(
    `${process.env.REACT_APP_BACKEND_URL}`,
    {
      path: `${process.env.REACT_APP_MONUMENTAL_TICKER_WS_PATH}`,
      secure: true,
      withCredentials: true,
      transports: ["websocket"],
      extraHeaders: {
        [`${process.env.REACT_APP_WS_SECRET_HEADER}`]: `${process.env.REACT_APP_WS_SECRET_HEADER_VALUE}`,
      },
      auth: {
        [`${process.env.REACT_APP_WS_SECRET_HEADER}`]: `${process.env.REACT_APP_WS_SECRET_HEADER_VALUE}`,
      },
    }
  )
  const [isShutdown, setIsShutdown] = useState(false)
  const [animationKey, setAnimationKey] = useState(0)
  const [animation, setAnimation] = useState(null)

  const tickerRef = useRef(null)
  const containerRef = useRef(null)
  const containerWrapRef = useRef(null)

  const { PUBLICITY_MESSAGES } = useFetchTickerPublicity(isShutdown)

  const { calculateAndSetAnimation } = useCalculateAndSetAnimation(
    tickerRef,
    containerRef,
    containerWrapRef,
    isShutdown,
    tickerParameters.speed,
    animation
  )

  const { leftMessagesRefHistoric } = useCheckFetchNewMessage(
    emit,
    tickerRef,
    containerRef,
    calculateAndSetAnimation,
    messagesItems,
    tickerParameters.speed,
    isShutdown,
    setFetchMessages,
    animation
  )

  const { HandleReceiveMessage } = useManageMessages(
    emit,
    tickerParameters,
    repeat,
    setRepeat,
    repetitionMessages,
    setRepetitionMessages,
    currentRepetitionIndex,
    setCurrentRepetitionIndex,
    currentRepetitions,
    setCurrentRepetitions,
    setMessagesItems,
    PUBLICITY_MESSAGES,
    newMessageData,
    setNewMessageData,
    fetchMessages,
    isShutdown
  )


  useEffect(() => {
    if (tickerRef.current) {
      console.log("Creating animation")
      const animation = containerWrapRef.current.animate(
        [ {transform: `translateX(0)`}, {transform: `translateX(-150%)`}],
        {duration: containerWrapRef.current.offsetWidth * (1400 / tickerParameters.speed), iterations: 1}
      )
      animation.addEventListener("finish", () => {
        console.log("animation finished")
      })
      setAnimation(animation)
    }

    on(MonumentalTickerSocketEvents.SHUTDOWN_TICKER, (data) => {
      setIsShutdown((prev) => {
        if (data === true && !prev) {
          return true
        }
        if (data === false && prev) {
          setFetchParameters((prev) => prev + 1)
          return false
        }
      })
    })

    on(MonumentalTickerSocketEvents.SEND_MESSAGE_TO_TICKER, (data) => {
      if (data && data.message) {
        HandleReceiveMessage(data)
      }
    })

    on(MonumentalTickerSocketEvents.TICKER_PARAMS_CHANGED, (data) => {
      setTickerParameters((prev) => {
        return { ...prev, ...data }
      })
    })

    return () => {
      off(MonumentalTickerSocketEvents.SHUTDOWN_TICKER)
      off(MonumentalTickerSocketEvents.SEND_MESSAGE_TO_TICKER)
      off(MonumentalTickerSocketEvents.TICKER_PARAMS_CHANGED)
      off(MonumentalTickerSocketEvents.NO_NEW_MESSAGES)
    }
  }, [HandleReceiveMessage])

  useEffect(() => {
    if (isShutdown) {
      if (animation)
        animation.cancel()
      setMessagesItems([])
      setCurrentRepetitionIndex(0)
      setCurrentRepetitions(0)
      setRepetitionMessages([])
      setRepeat(0)
      setNewMessageData(null)
    } else {
      if (animation)
        animation.play()
      if (messagesItems.length === 0) {
        setMessagesItems([DEFAULT_INITIAL_MESSAGE])
        calculateAndSetAnimation()
      }
    }
  }, [isShutdown])

  useEffect(() => {
    setIsShutdown(tickerParameters.shutdown)
  }, [tickerParameters])

  return (
    <React.Fragment>
      <div className="container" style={{ ...tickerCS }}>
        <div
          className="logo-container"
          style={{
            backgroundColor: isShutdown
              ? "#223249"
              : tickerParameters.model == tickerModels.TRIBU_TICKER
              ? "#008D36"
              : "#DF08A8",
          }}
        >
          {!isShutdown && (
            <img
              src={
                tickerParameters.model == tickerModels.TRIBU_TICKER
                  ? TribuLogo
                  : AqusticoLogo
              }
              alt="logo"
              height={"88px"}
            />
          )}
        </div>
        <div
          className="ticker-container"
          ref={containerRef}
          style={{
            backgroundColor: isShutdown
              ? "#E2E2E2"
              : tickerParameters.model == tickerModels.TRIBU_TICKER
              ? "#191F2B"
              : "#1C1468",
          }}
        >
          <div className="ticker-wrap" ref={containerWrapRef}>
            {!isShutdown && messagesItems.length > 0 && (
              <div
                ref={tickerRef}
                style={{
                  display: "flex",
                  width: "fit-content",
                  color:
                    tickerParameters.model == tickerModels.TRIBU_TICKER
                      ? "#CDFF2C"
                      : "white",
                }}
              >
                {messagesItems.map((item, index) => (
                  <div
                    key={index}
                    className="ticker-item"
                    offset={item.offset || 0}
                  >
                    <div>{item.message}</div>
                    {index < messagesItems.length - 1 && (
                      <div
                        key={`separator-${index}`}
                        className="ticker-separator"
                      >
                        <img
                          src={tickerParameters.separator}
                          alt="separator"
                          width={`${tickerParameters.separatorWidth}px`}
                          height={`${tickerParameters.separatorHeight}px`}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
            {isShutdown && (
              <div className="ticker-item" style={{ color: "white" }}></div>
            )}
          </div>
        </div>
      </div>

      {isShutdown && solo ? (
        <div
          style={{
            position: "absolute",
            top: "228px",
            width: "100%",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <p
            style={{
              color: "#DADADA",
              fontSize: "20px",
              fontWeight: "600",
              fontFamily: "Inter",
              fontStyle: "normal",
              lineHeight: "24px",
              letterSpacing: "0.15px",
            }}
          >
            No hay cintillo activo en este momento
          </p>
        </div>
      ) : null}
    </React.Fragment>
  )
}

Ticker.propTypes = {
  tickerCS: PropTypes.object.isRequired,
  solo: PropTypes.bool,
}

export default Ticker
