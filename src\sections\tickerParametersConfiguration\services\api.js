import { get, put, post} from '../../../helpers/axios';

const BASE_PATH = '/monumental-ticker-parameters';

export const getAllParameters = () => get(`${BASE_PATH}/getAll`);
export const getAllPublicity = () => get(`${BASE_PATH}/getAllPublicity`);
export const updateParameters = (parameters) => put(`${BASE_PATH}/updateTickerParams`, parameters);
export const createPublicity = (publicity) => post(`${BASE_PATH}/createTickerPublicity`, publicity);
export const deletePublicity = (id) => post(`${BASE_PATH}/deleteTickerPublicity`, id);
