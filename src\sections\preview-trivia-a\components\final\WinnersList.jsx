import React from 'react';
import PropTypes from 'prop-types';
import corona from '../../../../assets/corona.png';

const WinnersListAQ = ({ winners, ranking }) => {

  const columns = Math.ceil(winners.length / 5);

  return (
    <div
      id="winners-section"
      style={{
        display: "grid",
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: "20px",
        height:'65vh',
        width: winners.length <= 5 ? "40%" : "85%", // Ajustar ancho según cantidad de ganadores
        margin: "0 auto",
      
      }}
    >
      {Array.from({ length: columns }).map((_, colIndex) => (
        <div key={colIndex} style={{ display: "flex", flexDirection: "column" }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "20% 40% 40%",
              alignItems: "center",
              fontSize: "20px",
              fontWeight: "bold",
              color:"#FFF",
              backgroundColor: "transparent",
              borderRadius: "10px",
              padding: "10px",
              boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)" ,
            }}
          >
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center" , opacity: colIndex === 0 ? 1 : 0 }}>
              <img
                src={corona}
                style={{ height: "8vh" }}
                alt="Corona"
              />
            </div>
            <div
              style={{
                textAlign: "center",
                fontFamily: "Montserrat",
                 opacity: colIndex === 0 ? 1 : 0,
                fontSize: winners.length === 5 ? "4vh" : "3.5vh",
                fontWeight: "700",
                
              }}
            >
              {"Alias"}
            </div>
            <div
              style={{
                textAlign: "center",
                 opacity: colIndex === 0 ? 1 : 0,
                fontFamily: "Montserrat",
                fontSize: winners.length === 5 ? "4vh" : "3.5vh",
                fontWeight: "700",
              
              }}
            >
              {"Puntaje"}
            </div>
          </div>
          {winners
            .slice(colIndex * 5, colIndex * 5 + 5)
            .map((winner, index) => (
              <div
                key={index}
                style={{
                  display: "grid",
                  gridTemplateColumns: "20% 40% 40%",
                  alignItems: "center",
                  backgroundColor: "#FFFFFF40",
                  borderRadius: "10px",
                  padding: "0.5vh",
                  boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                  fontSize: "4vh",
                  fontWeight: "bold",
                  color: "#FFF",
                  marginBottom: "20px", // Separación entre cada ganador
                  outline: (colIndex * 5 + index + 1) <= ranking ? "5px solid #CDFF2C" : "none", // Usar outline en lugar de border para evitar agrandar el contenedor
                }}
              >
                <div
                  style={{
                    textAlign: "center",
                    fontSize: "5vh",
                    fontWeight: "900",
                    position: "relative",
                    color: (colIndex * 5 + index + 1) <= ranking ? "#CDFF2C" : "#FFF"
                  }}
                >
                  {colIndex * 5 + index + 1}
                  <div
                    style={{
                      position: "absolute",
                      top: "50%",
                      left: "100%",
                      width: "2px",
                      height: "40px",
                      backgroundColor: "#FFF",
                      transform: "translateY(-50%)",
                    }}
                  ></div>
                </div>
                <div
                  style={{
                    textAlign: "center",
                    fontSize: "3vh",
                    position: "relative",
                  }}
                >
                  {winner.id_user.alias}
                  <div
                    style={{
                      position: "absolute",
                      top: "50%",
                      left: "100%",
                      width: "2px",
                      height: "40px",
                      backgroundColor: "#FFF",
                      transform: "translateY(-50%)",
                    }}
                  ></div>
                </div>
                <div style={{ textAlign: "center", fontSize: "3vh" }}>
                  {winner.total_points} PTS
                </div>
              </div>
            ))}
        </div>
      ))}
    </div>
  );
};

WinnersListAQ.propTypes = {
  winners: PropTypes.any.isRequired,
  ranking: PropTypes.any.isRequired, 
};

export default WinnersListAQ;
