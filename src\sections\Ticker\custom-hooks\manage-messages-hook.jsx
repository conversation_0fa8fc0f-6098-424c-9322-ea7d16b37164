import { useCallback, useEffect, useState } from "react"
import { MonumentalTickerSocketEvents } from "../socket-events/monumental-ticker-socket-event"
import moment from "moment"

const DEFAULT_OFFSET = -4

export const useManageMessages = (
  emit,
  tickerParameters,
  repeat,
  setRepeat,
  repetitionMessages,
  setRepetitionMessages,
  currentRepetitionIndex,
  setCurrentRepetitionIndex,
  currentRepetitions,
  setCurrentRepetitions,
  setMessagesItems,
  PUBLICITY_MESSAGES,
  newMessageData,
  setNewMessageData,
  messagesFetched,
  shutdown
) => {
  const [messageItemsPool, setMessagesItemsPool] = useState([])
  const [currentPublicityIndex, setCurrentPublicityIndex] = useState(0)

  useEffect(()=>{
    const intervalId = setInterval(() => {
      if (messageItemsPool.length > 10 || shutdown) return
      emit(MonumentalTickerSocketEvents.TICKER_REQUEST_MESSAGE)
    }, 1000)

    return () => {
      clearInterval(intervalId)
    }
  }, [])

  const HandleReceiveMessage = useCallback(
    (data) => {
      if (
        moment()
          .utcOffset(DEFAULT_OFFSET)
          .startOf("day")
          .add(parseInt(tickerParameters.closeHour.split(":")[0]), "hour")
          .add(parseInt(tickerParameters.closeHour.split(":")[1]), "minute") >
        moment(data.date).utcOffset(-DEFAULT_OFFSET)
      )
        setRepeat((prev) => prev + 1)
      else
        setNewMessageData((prev) => {
          return data
        })
    },
    [tickerParameters.closeHour]
  )

  const handleNoNewMessages = useCallback(() => {
    if (repeat == 0) return
    if (repetitionMessages.length > 0) {
      if (currentRepetitionIndex + 1 == repetitionMessages.length) {
        setCurrentRepetitionIndex(0)
        if (currentRepetitions + 1 >= tickerParameters.maxRepetitions) {
          setCurrentRepetitions(0)
          setRepetitionMessages([])
        } else setCurrentRepetitions((prev) => prev + 1)
      } else {
        setCurrentRepetitionIndex((prev) => {
          return prev + 1
        })
      }
      setMessagesItems((prev) => {
        let aux = [...prev]
        aux.push({...repetitionMessages[currentRepetitionIndex], offset: messagesFetched})
        return aux
      })
    } else {
      const message = PUBLICITY_MESSAGES[currentPublicityIndex]
      if (currentPublicityIndex + 1 == PUBLICITY_MESSAGES.length) {
        setCurrentPublicityIndex(0)
      } else {
        setCurrentPublicityIndex((prev) => prev + 1)
      }
      setMessagesItems((prev) => [...prev, {...message, offset: messagesFetched}])
    }
  }, [
    repetitionMessages,
    currentRepetitionIndex,
    currentRepetitions,
    tickerParameters.maxRepetitions,
    repeat,
  ])

  const handleNewMessage = useCallback(
    (data) => {
      if (currentRepetitionIndex > 0 || currentRepetitions > 0) {
        setCurrentRepetitionIndex(0)
        setCurrentRepetitions(0)
        setRepetitionMessages([])
      }
      setMessagesItems((prev) => [
        ...prev,
        { id: data.id, message: data.message, offset: messagesFetched },
      ])
      setRepetitionMessages((prev) => [
        ...prev,
        { id: data.id, message: data.message, offset: messagesFetched },
      ])
    },
    [currentRepetitionIndex, currentRepetitions]
  )
  useEffect(() => {
    handleNoNewMessages()
  }, [repeat])

  useEffect(() => {
    if (newMessageData != null) {
      setMessagesItemsPool((prev) => [...prev, newMessageData])
      setNewMessageData(null)
    }
  }, [newMessageData])

  useEffect(() => {
    if (messageItemsPool.length > 0) {
      handleNewMessage({...messageItemsPool[0], offset: messagesFetched})
      setMessagesItemsPool((prev) => prev.slice(1))
    } else {
      setRepeat((prev) => prev + 1)
    }
  }, [messagesFetched])

  return { HandleReceiveMessage }
}
