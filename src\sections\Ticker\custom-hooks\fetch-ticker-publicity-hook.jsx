import { useEffect, useState } from "react"
import { getAllPublicity } from "../services/api"
import { debounce } from "lodash"

export const useFetchTickerPublicity = (isShutdown) => {
  const [publicityFetched, setPublicityFetched] = useState(false)
  const [PUBLICITY_MESSAGES, setPUBLICITY_MESSAGES] = useState([
    {
      id: -1,
      message: "Con Tribu siempre ganas",
    },
  ])

  const fetchTickerPublicity = debounce(async () => {
    try {
      const response = await getAllPublicity()
      const publicity = response.data
      if (publicity.length > 0)
        setPUBLICITY_MESSAGES((prev) => publicity)
    } catch (error) {
      console.error("Error fetching ticker publicity:", error)
    }
  }, 1000)
  
  useEffect(async () => {
    if (publicityFetched) return
    setPublicityFetched((prev)=>true);
    await fetchTickerPublicity()
  }, [isShutdown])

  return { PUBLICITY_MESSAGES }
}
