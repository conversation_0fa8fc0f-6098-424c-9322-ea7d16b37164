import React, { useState } from "react"
import PropTypes from "prop-types"
import { useApp } from "../../../../AppProvider"
import { useNavigate } from "react-router"
import {
  Button,
  Grid,
  Typography,
  IconButton,
  TextField,
  Tooltip,
  Box,
  DialogActions,
  Dialog,
  DialogContent,
} from "@mui/material"
import { makeStyles } from "@mui/styles"
import MenuItem from "@mui/material/MenuItem"
import { useCallback, useEffect } from "react"
import { useForm, Controller } from "react-hook-form"
import AttachFileIcon from "@mui/icons-material/AttachFile"
import CircularProgress from "@mui/material/CircularProgress"
import * as moment from "moment"
import "moment/locale/es"
import { createTheme } from "@mui/material/styles"
import {
  campaignFormConfig,
} from "../../constants/constants"
import ConfirmDialog from "../../../../components/ConfirmDialog/ConfirmDialog"
import BackButton from "../../../../components/BackButton"
import SecundaryButton from "../../../../components/SecundaryButton/SecundaryButton"
import PrimaryButton from "../../../../components/PrimaryButton/PrimaryButton"
import PublicityFormModal from './PublicityFormModal';
import QuestionFormModal from './QuestionFormModal';
import EditIcon from '@mui/icons-material/Edit';
import { min } from "date-fns"

moment.locale("es")

const useStyles = makeStyles(() => ({
  baseActionButton: {
    fontSize: "14px !important",
    width: "130px",
    fontfamily: "Ubuntu !important",
    fontWeight: "600 !important",
    lineHeight: "28px !important",
    backgroundColor: "white !important",
    padding: "5px  !important",
    border: "none",
    boxShadow:
      "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1) !important",
    borderRadius: "8px !important",
    color: "#D9D9D9 !important",
    textTransform: "none !important",
    "@media screen and (maxWidth: 400px)": {
      fontSize: "0.4rem",
    },
    fontFamily: "Ubuntu !important",
  },
  nextActionButton: {
    backgroundColor: "#4E5589 !important",
    color: "white !important",
  },
  nextActionButtonDisabled: {
    backgroundColor: "#CFD8E0 !important",
  },
  baseInputStyle: {
    backgroundColor: "white",
  },
  baseInputLabel: {
    backgroundColor: "white",
    "& .MuiFormHelperText-contained": {
      backgroundColor: "#F5F4F4",
      margin: 0,
      paddingLeft: "2px",
      paddingTop: "5px",
    },
    "& .MuiInputLabel-root": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiInputLabel-shrink": {
      color: "#000000",
    },
    "& .MuiButtonBase-root": {
      color: "black",
    },
  },
  select: {
    backgroundColor: "white",
    "&:after": {
      borderBottomColor: "darkred",
    },
    "& .MuiInputLabel-root": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiSvgIcon-root": {
      color: "#009EFF",
      fontSize: "3.2rem",
      height: "56px",
      borderLeft: "1px solid #C4C4C4",
      borderRadius: "1",
      right: "0px",
      top: "0px",
      width: "45px",
      borderColor: "transparent",
    },
    "& .MuiFormHelperText-contained": {
      margin: 0,
      paddingLeft: "10px",
      paddingTop: "5px",
    },
    "&:click ": {
      color: "red",
    },
  },
  fileUpload: {
    backgroundColor: "white",
    "& .MuiFormHelperText-contained": {
      margin: 0,
      paddingLeft: "10px",
      paddingTop: "5px",
    },
    "&:after": {
      borderBottomColor: "darkred",
    },
    "& .MuiInputLabel-root": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiInputBase-root": {
      padding: "0",
    },
    "& .MuiButtonBase-root": {
      padding: "11px",
    },
    "&:click ": {
      color: "red",
    },
  },
  fileHasErrors: {
    color: "#FF4000",
  },
  fileHasNoErrors: {
    color: "#009EFF",
  },
  statisticsContainer: {
    marginTop: "20px",
    width: "97%",
    marginLeft: "3%",
    border: "1px solid #C4C4C4",
    borderRadius: "4px",
    backgroundColor: "#E3E8EE",
    padding: "10px 20px 10px 20px",
    fontFamily: "Ubuntu",
  },
  statisticsBoldText: {
    fontWeight: "bold",
    fontSize: "11px",
  },
  statisticsNormalText: {
    fontWeight: "normal",
    fontSize: "12px",
    marginLeft: "15px",
  },
}))

function CircularProgressWithLabel(props) {
  return (
    <Box sx={{ position: "relative", display: "inline-flex" }}>
      <CircularProgress variant="determinate" {...props} value={props.value} />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: "absolute",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography
          variant="caption"
          component="div"
          sx={{ color: "#000000", fontSize: "10px" }}
        >{`${Math.round(props.value)}%`}</Typography>
      </Box>
    </Box>
  )
}

CircularProgressWithLabel.propTypes = {
  value: PropTypes.number.isRequired,
}

const MyActionBar = ({ onAccept, onCancel }) => {
  return (
    <DialogActions>
      <Button onClick={onCancel}> CANCELAR </Button>
      <Button onClick={onAccept}> OK </Button>
    </DialogActions>
  )
}

MyActionBar.propTypes = {
  onAccept: PropTypes.any,
  onCancel: PropTypes.any,
}

const CampaignForm = ({ campaignInfo, formType, disableInputs }) => {

  const { showNotification } = useApp()

  const navigate = useNavigate()
  const classes = useStyles()

  const [base64Image, setBase64Image] = useState('')

  const [newDate, setNewDate] = useState(null)

  const [openModal, setOpenModal] = useState(false)
  const [modalConfig, setModalConfig] = useState({
    type: "",
    title: "",
    nextButtonLabel: "",
    backButtonLabel: "",
    message: "",
  })

  const [formConfig, setFormConfig] = useState({
    type: "",
    submitAction: () => ({}),
    title: "",
    actionButton: {
      submit: "",
      cancel: "",
    },
    notifications: {
      success: "",
      fail: "",
    },
    modals: {
      submit: "",
      back: "",
    },
  })

  const theme = createTheme({
    components: {
      MuiPickersToolbar: {
        styleOverrides: {
          penIconButton: {
            pointerEvents: "none",
            visibility: "hidden",
          },
        },
      },
      MuiTimePickerToolbar: {
        styleOverrides: {
          ampmSelection: {
            pointerEvents:
              newDate &&
              moment(newDate).isSame(moment(), "day") &&
              moment().isSameOrAfter(moment().hours(12), "hours")
                ? "none"
                : "",
          },
        },
      },
    },
  })

  const {
    handleSubmit,
    setValue,
    reset,
    setError,
    watch,
    trigger,
    control,
    getValues,
    formState: { isValid, dirtyFields, errors },
  } = useForm({
    mode: "all",
    defaultValues: {
      title: "",
      product: "",
      questionDuration:'',
      startDuration:'',
      logo:null,
      publicity: {
          description: '',
          url_mob: null,
          url_web: null,
          duration: '',
      },
      winners: "",
      questions: [
        {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
         {
          question: "",
          answer: [
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
            { text: "", correct: false },
          ],
        },
      ],
    },
  })

  const {
    publicity,
    product,
    title,
    logo,
    winners,
    questions,
    questionDuration,
    startDuration
    
  } = watch()

  const fetchBusiness = useCallback(function() {
    /* setDisabledInputs({ ...disabledInputs, ...disableInputs }) */
    setFormConfig(campaignFormConfig.find((config) => config.type === formType));
    /* const availableBusiness = await getAvailableBusiness()
    setBusiness(availableBusiness.data) */
/*     if (campaignInfo) {
    prepareData(campaignInfo);
  } */
  }, [])
  useEffect(() => {
    fetchBusiness()
  }, [fetchBusiness])

  const onSubmit = async function() {
    console.log("onSubmit called")
    const formData = formattedDataToSubmit()
      console.log("formDataToSubmit", formData)

    try {
      const response = await formConfig.submitAction(formData);
      setOpenModal(false);
      showNotification(formConfig.notifications.success, "success");
      onBackButtonClick();
    } catch (err) {
      if (err.response) {
        if (err.response.data.message.includes("UQ_TITLE_PROD")) {
          setError("title", {
            type: "custom",
            message: `Lo sentimos, el título ingresado está en uso por otra campaña`,
          })
          document
            .getElementById("title-input")
            .scrollIntoView({ behavior: "instant", block: "center" })
        } else {
          showNotification(formConfig.notifications.fail, "error")
        }
      } else {
        showNotification(formConfig.notifications.fail, "error")
      }
      setOpenModal(false)
    }
  }

  const formattedDataToSend = () => {
    const values = getValues();

    // Formatear las preguntas y respuestas
    const formattedQuestions = values.questions.map((question) => {
      return {
        question: question.question,
        answer: question.answer.map((answer) => ({
          text: answer.text,
          correct: answer.correct,
        })),
      };
    });

    return {
      title: values.title,
      product: values.product,
      publicity: values.publicity,
      logo: values.logo,
      winners: values.winners,
      questionDuration: values.questionDuration,
      startDuration: values.startDuration,
      questions: formattedQuestions,
    };
  };

  const formattedDataToSubmit = () => {
    const values = getValues();

    const formData = new FormData();

    // Agregar campos al FormData
    formData.append("title", values.title);
    formData.append("product", values.product);
    formData.append("winners", values.winners);
    formData.append("questionDuration", values.questionDuration );
    formData.append("startDuration", values.startDuration);
    if(values.logo){
       formData.append("logo", values.logo);
    }
    

    // Agregar publicidad al FormData
    if (values.publicity) {
      formData.append("publicity[description]", values.publicity.description);
      if (values.publicity.url_mob) {
        formData.append("url_mob", values.publicity.url_mob);
      }
      if (values.publicity.url_web) {
        formData.append("url_web", values.publicity.url_web);
      }
      formData.append("publicity[duration]", values.publicity.duration ? values.publicity.duration : 10);
    }

    // Agregar preguntas y respuestas al FormData
    values.questions.forEach((question, index) => {
      formData.append(`questions[${index}][question]`, question.question);
      question.answer.forEach((answer, answerIndex) => {
        formData.append(`questions[${index}][answer][${answerIndex}][text]`, answer.text);
        formData.append(`questions[${index}][answer][${answerIndex}][correct]`, answer.correct);
      });
    });

    return formData;
  };
  
  const onBackButtonClick = () => {
    navigate("/trivia")
  }

  const onShowModal = () => {
    setOpenModal(!openModal)
  }

  const showCreateCampaignMessageModal = () => {
    const formData = formattedDataToSend();
    console.log("formData", formData);

      // Validación de publicidad
    if (!formData.publicity || !formData.publicity.description || !formData.publicity.url_mob || !formData.publicity.url_web || !formData.publicity.duration) {
      showNotification(
        "La publicidad no está completa. Asegúrate de llenar todos los campos de publicidad.",
        "error"
      );
      return;
    }


    // Validación de respuestas correctas
    const questionsWithoutCorrectAnswer = formData.questions
      .map((question, index) => ({
        index: index + 1,
        hasCorrectAnswer: question.answer.some((answer) => answer.correct),
      }))
      .filter((q) => !q.hasCorrectAnswer);

    if (questionsWithoutCorrectAnswer.length > 0) {
      const questionNumbers = questionsWithoutCorrectAnswer.map((q) => q.index).join(", ");
      showNotification(
        `Las siguientes preguntas no tienen al menos una respuesta marcada como correcta: ${questionNumbers}.`,
        "error"
      );
      return;
    }

  
    setModalConfig({
      ...formConfig.modals.submit,
      nextButtonAction: handleSuccessCampaignMessageModal,
    });
    setOpenModal(true);
  }

  const showCancelCampaignMessageModal = async () => {
    if (
      Object.keys(dirtyFields).length !== 0 ||
      title ||
      product ||
      publicity ||
      winners ||
      questionDuration ||
      startDuration 
    ) {
      setModalConfig({
        ...formConfig.modals.back,
        nextButtonAction: handleCancelCampaignMessageModal,
      })
      setOpenModal(true)
    } else {
      onBackButtonClick()
    }
  }

  const getModalMessage = () => {
    let modalMessage = ""
    if (formConfig.type === "edit" && modalConfig.type === "submit") {
        modalMessage = `¿Estás seguro que deseas editar la siguiente trivia: <b>${title}</b>?`
    } else if (formConfig.type === "new" && modalConfig.type === "submit") {
        modalMessage = `¿Estás seguro que deseas crear la siguiente trivia: <b>${title}</b>?`
    } else {
      modalMessage = `¿Estás seguro que deseas salir? La información cargada hasta el momento no se guardará.`
    }

    return modalMessage
  }
  const handleSuccessCampaignMessageModal = async () => {
    if (isValid) {
      onSubmit()
    }
  }

  const handleCancelCampaignMessageModal = async () => {
    setOpenModal(false)
    onBackButtonClick()
  }

  const [openPublicityModal, setOpenPublicityModal] = useState(false);
  const [publicityData, setPublicityData] = useState({});

  const handleOpenPublicityModal = () => {
    setOpenPublicityModal(true);
  };

  const handleClosePublicityModal = () => {
    setOpenPublicityModal(false);
  };

  const handleSavePublicity = (data) => {
    setPublicityData(data);
    console.log("Publicity data saved:", data);
    setValue("publicity", data, { shouldDirty: true });
  };

  const [openQuestionModal, setOpenQuestionModal] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(null);

  const handleOpenQuestionModal = (index) => {
    console.log("Opening question modal for index:", index);
    console.log("Opening question modal for index:", questions);
    setCurrentQuestionIndex(index);
    setOpenQuestionModal(true);
  };

  const handleCloseQuestionModal = () => {
    setOpenQuestionModal(false);
    setCurrentQuestionIndex(null);
  };

  const [openPreviewModal, setOpenPreviewModal] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);

  const handleOpenPreviewModal = () => {
    const logoFile = getValues('logo');
    if (logoFile) {
      const logoURL = URL.createObjectURL(logoFile);
      setPreviewImage(logoURL);
      setOpenPreviewModal(true);
    } else {
      alert('No se ha subido ningún logo.');
    }
  };

  const handleClosePreviewModal = () => {
    setOpenPreviewModal(false);
    setPreviewImage(null);
  };

  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const img = new Image();
      img.onload = () => {
        if (img.width > 130 || img.height > 123 || file.size > 5 * 1024 * 1024) {
          document.getElementById('logo-error').style.color = 'red';
          setValue('logo', null, { shouldDirty: true });
        } else {
          document.getElementById('logo-error').style.color = 'black';
          setValue('logo', file, { shouldDirty: true });
        }
      };
      img.src = URL.createObjectURL(file);
    }
  };

  return (
    <>
      <Grid container direction="row" wrap="nowrap" paddingBottom={"30px"}>
        <Grid
          item
          container
          xs={2}
          sm={2}
          md={2}
          direction="row"
          paddingLeft={"34px"}
        >
          {formConfig.type === "edit" && (
            <Grid item>
              <BackButton action={showCancelCampaignMessageModal} />
            </Grid>
          )}
        </Grid>
        <Grid item container direction="column" xs={6} sm={6} md={6}>
          <Grid item>
            <Typography
              variant="h3"
              sx={{
                fontFamily: "Ubuntu !important",
                fontStyle: "normal !important",
                fontWeight: "700 !important",
                fontSize: "25px !important",
                lineHeight: "24px !important",
                letterSpacing: "0.15px !important",
                color: "#363636 !important", 
                marginTop: "9px",
                marginBottom: "10px",
              }}
            >
             Nueva Trivia
            </Typography>
          
          </Grid>
          <Grid item container direction="column" sx={{ marginTop: "30px" }}>
            <form
              onSubmit={handleSubmit(showCreateCampaignMessageModal)}
              style={{ width: "100%" }}
            >
              <Grid container direction="column" spacing={2}>
                <Grid
                  item
                  container
                  direction="row"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Grid item xs={12} sm={12} md={12}>
                    <Controller
                      name="product"
                      control={control}
                      rules={{ required: "Campo obligatorio" }}
                      render={({ field, fieldState: { error }, formState }) => (
                        <TextField
                          id="product-input"
                          select
                          fullWidth
                          label="Producto"
                          helperText={error ? error.message : null}
                          error={!!error}
                          className={classes.select}
                          {...field}
                        >
                         
                          <MenuItem
                            sx={{ maxWidth: "600px" }}
                            key={'tribu'}
                            value={'tribu'}
                          >
                            Tribudeportiva
                          </MenuItem>

                     {/*      <MenuItem
                            sx={{ maxWidth: "600px" }}
                            key={'aqustico'}
                            value={'aqustico'}
                          >
                            Aqustico
                          </MenuItem> */}
                        
                        </TextField>
                      )}
                    />
                  </Grid>
                </Grid>
              
          
                <Grid
                  item
                  container
                  direction="row"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Grid item xs={12} sm={12} md={12}>
                    <Controller
                      name="title"
                      control={control}
                      rules={{ required: "Campo obligatorio" }}
                      render={({ field, fieldState: { error } }) => (
                        <TextField
                          id="title-input"
                          label="Título"
                          fullWidth
                          variant="outlined"
                         
                          className={classes.baseInputLabel}
                          helperText={error ? error.message : null}
                          error={!!error}
                          {...field}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
                      <Grid
                  item
                  container
                  direction="row"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Grid item xs={8} sm={8} md={8}>
                     <Button
                      component="label"
                      fullWidth
                      style={{  border: '1px solid gray', 
                      fontSize: '16px', textTransform: 'none', height: '56px',
                      fontWeight: '400', display: 'flex', alignItems: 'center', justifyContent: 'space-between', 
                      paddingLeft: '15px' }}
                    >
                      <Typography style={{color:'black', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}> {logo ?  logo.name   :   'Subir Logo'}</Typography>
                      <AttachFileIcon ></AttachFileIcon>
                      <input
                        type="file"
                        name="logo"
                       accept=".png,.svg,.webp"
                        hidden
                        onChange={handleLogoUpload}
                      />
                    </Button>
                    <div id="logo-error" style={{ marginLeft: '5px', fontSize: 12, marginTop: '5px' }}>
                      Tamaño max 130x123px y 5mb.
                    </div>
                    </Grid>
                  <Grid item xs={2} sm={2} md={2}>
                    {logo && (
                       <Button
                      variant="contained"
                      fullWidth
                      style={{ backgroundColor: '#009EFF', color: 'white', }}
                      onClick={handleOpenPreviewModal}
                    >
                      Ver Logo
                    </Button>
                    )}
                   
                  </Grid>
                </Grid>
                <Grid
                  item
                  container
                  direction="row"
                  alignItems="center"
                  spacing={2}
                  justifyContent="center"
                >
                  <Grid item xs={4} sm={4} md={4} >
                    <Controller
                      name="winners"
                      control={control}
                      rules={{ required: "Campo obligatorio" }}
                      render={({ field, fieldState: { error }, formState }) => (
                        <TextField
                          id="winners-input"
                          select
                          fullWidth
                         
                          label="Cantidad de Ganadores"
                          helperText={error ? error.message : null}
                          error={!!error}
                          className={classes.select}
                          {...field}
                        >
                            <MenuItem
                              sx={{ maxWidth: "600px" }}
                              key={5}
                              value={5}
                            >
                              5
                            </MenuItem>

                            <MenuItem
                              sx={{ maxWidth: "600px" }}
                              key={10}
                              value={10}
                            >
                              10
                            </MenuItem>
                            <MenuItem
                              sx={{ maxWidth: "600px" }}
                              key={15}
                              value={15}
                            >
                              15
                            </MenuItem>
                          {/*   <MenuItem
                            sx={{ maxWidth: "600px" }}
                            key={20}
                            value={20}
                          >
                            20
                          </MenuItem> */}
                        
                        </TextField>
                      )}
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} md={4}>
                    <Controller
                      name="questionDuration"
                      control={control}
                      rules={{ required: "Campo obligatorio", min: { value: 10, message: "Debe ser mayor a 10" } }}
                      render={({ field, fieldState: { error }, formState }) => (
                        <TextField
                          id="duration-input"
                          fullWidth
                          label="Duración de Preguntas (segundos)"
                          type="number"
                          helperText={error ? error.message : null}
                          error={!!error}
                          className={classes.baseInputLabel}
                          {...field}
                        >
                        </TextField>
                      )}
                    />
                  </Grid>
                  <Grid item xs={4} sm={4} md={4}>
                    <Controller
                      name="startDuration"
                      control={control}
                      rules={{ required: "Campo obligatorio" , min: { value: 10, message: "Debe ser mayor a 10" } }}
                      render={({ field, fieldState: { error }, formState }) => (
                        <TextField
                          id="start-input"
                          fullWidth
                          type="number"
                          label="Inicio de Trivia (segundos)"
                          helperText={error ? error.message : null}
                          error={!!error}
                          className={classes.baseInputLabel}
                          {...field}
                        >
                        </TextField>
                      )}
                    />
                  </Grid>
                </Grid>
                 <Grid
                  item
                  container
                  direction="row"
                  alignItems="center"
                  justifyContent="start"
                >
                  <Grid item xs={6} sm={6} md={6} style={{display:'flex',alignItems:'center'}}> <Typography>Publicidad</Typography>
            
                    </Grid>
                    <Grid item xs={6} sm={6} md={6} style={{display:'flex',alignItems:'center', justifyContent:'end'}}>
               <Button
                  style={{border:'2px solid  #009EFF',background:'white', fontSize:'14px',
                     fontWeight:'600', textTransform:'none'}}
                   
                onClick={() => handleOpenPublicityModal()} >
                  
              <AttachFileIcon></AttachFileIcon>
           
                </Button>
                  </Grid>
                
                </Grid>
        
              {/* preguntas */}
                <Grid
                  item
                  container
                  direction="row"
                  alignItems="center"
                  justifyContent="start"
                >
                  <Grid item xs={6} sm={6} md={6} style={{display:'flex',alignItems:'center'}}> <Typography>Preguntas</Typography>
            
                    </Grid>
                    <Grid item xs={6} sm={6} md={6} style={{display:'flex',alignItems:'center', justifyContent:'end'}}>
                 
                      </Grid>
                
                </Grid>

                <Grid
                  item
                  container
                  direction="row"
                  spacing={2}
                >
                  {questions.length > 0 && (
                    <React.Fragment>
                      {questions.map((question, questionIndex) => (
                        <Grid
                          key={`question-${questionIndex}`}
                          item
                          xs={4}
                          style={{ display: 'flex', justifyContent: 'center' }}
                        >
                          <Button
                            style={{ width: '100%', background:'#009EFF' }}
                            variant="contained"
                            onClick={() => handleOpenQuestionModal(questionIndex)}
                            
                          >
                            Pregunta {questionIndex + 1}
                          </Button>
                        </Grid>
                      ))}
                    </React.Fragment>
                  )}
                </Grid>
             
             
             

                <Grid
                  item
                  container
                  direction="row"
                  justifyContent="center"
                  sx={{ marginTop: "36px", overflowY: "hidden", gap: "20px" }}
                >
                  <Grid item>
                    <SecundaryButton
                      text={'Cancelar'}
                      action={showCancelCampaignMessageModal}
                      width="125px"
                      height="40px"
                      fontSize="14"
                      lineHeight="28"
                      type="button"
                      noShadow={true}
                    />
                  </Grid>
                  <Grid item>
           
                      <PrimaryButton
                      text={formConfig.actionButton.submit}
                      action={() => {}}
                      width="125px"
                      height="40px"
                      fontSize="14"
                      lineHeight="28"
                      type="submit"
                      noShadow={true}
                      />
                   {/*  )} */}
                    
                  </Grid>
                </Grid>
              </Grid>
            </form>
          </Grid>
        </Grid>
        <ConfirmDialog
          shouldOpen={openModal}
          title={modalConfig.title}
          nextButtonLabel={modalConfig.nextButtonLabel}
          backButtonLabel={modalConfig.backButtonLabel}
          message={getModalMessage()}
          backButtonAction={onShowModal}
          nextButtonAction={modalConfig.nextButtonAction}
        />
        {/* Modal de Publicidad */}
        <PublicityFormModal
          open={openPublicityModal}
          publicityData={publicityData}
          onClose={handleClosePublicityModal}
          onSubmit={handleSavePublicity}
        />
        {/* Modal de Preguntas */}
        <QuestionFormModal
          open={openQuestionModal}
          onClose={handleCloseQuestionModal}
          questionIndex={currentQuestionIndex}
          questions={questions}
          setQuestions={(updatedQuestions) => setValue("questions", updatedQuestions)}
          initialData={questions[currentQuestionIndex] || { question: "", answer: [] }}
          onSave={(updatedQuestion, updatedAnswers) => {
            const updatedQuestions = [...questions];
            updatedQuestions[currentQuestionIndex] = updatedQuestion
            setValue("questions", updatedQuestions);
          }}
        />
        <Dialog open={openPreviewModal} onClose={handleClosePreviewModal} maxWidth="sm" fullWidth>
          <DialogContent style={{ textAlign: 'center', backgroundColor: 'rgba(0, 0, 0, 0.77)' }}>
            {previewImage && <img src={previewImage} alt="Preview Logo" style={{ maxWidth: '100%', maxHeight: '9vh' }} />}
          </DialogContent>
        </Dialog>
      </Grid>
    </>
  )
}

CampaignForm.propTypes = {
  campaignInfo: PropTypes.any,
  formType: PropTypes.oneOf(["edit", "new"]).isRequired,
  disableInputs: PropTypes.shape({
    disableBusiness: PropTypes.bool,
    disableProduct: PropTypes.bool,
    disableTitle: PropTypes.bool,
    disableMessage: PropTypes.bool,
    disableDate: PropTypes.bool,
    disableFile: PropTypes.bool,
    disableShortCodes: PropTypes.bool,
    disableEncrypt: PropTypes.bool,
  }),
}

export default CampaignForm
