import { createSvgIcon, Grid, Typography } from "@mui/material"
import PropTypes from "prop-types"
import Icon from "../../../../assets/exclamation.png"
import { makeStyles } from "@mui/styles"
import React, { useEffect, useState } from "react"
import { tickerModels } from "../../../Ticker/enums/ticker-models"
import { updateParameters } from "../../services/api"
import { ArrowDropDown } from "@mui/icons-material"
import CompoundInputNumber from "../../../../components/CompoundInputNumber"
import FileInputManagment from "../fileInputManagment/FileInputManagment"
import { tickerSizes } from "../../../Ticker/enums/ticker-sizes"
import { TickerSizePipe } from "../pipes/ticker-size-pipe"

const DownArrowIcon = createSvgIcon(
  <path
    d="M1 16.0125L1.8875 15.125L6 19.2375L10.1125 15.125L11 16.0125L6 21.0125L1 16.0125Z"
    fill="#1C1B1F"
  />,
  "down_arrow_icon"
)

const UpArrowIcon = createSvgIcon(
  <path
    d="M1 7.9875L1.8875 8.875L6 4.7625L10.1125 8.875L11 7.9875L6 2.9875L1 7.9875Z"
    fill="#1C1B1F"
  />,
  "up_arrow_icon"
)

const useStyles = makeStyles(() => ({
  parameterInput: {
    fontFamily: "Inter",
    fontSize: "15px",
    color: "#000000",
    fontWeight: "600",
    minHeight: "40px",
    borderRadius: "10px",
    textAlign: "center",
    border: "1px solid #000000",
    outline: "none",
    // Hide default number input arrows
    "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
      appearance: "none",
      margin: 0,
    },
    "&[type=number]": {
      MozAppearance: "textfield",
    },
  },
  selectInput: {
    appearance: "none",
    backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
    backgroundRepeat: "no-repeat",
    backgroundPosition: "right 12px center",
    backgroundSize: "16px",
  },
  labelInput: {
    fontWeight: "600",
    fontFamily: "Inter",
    fontSize: "15px",
    lineHeight: "24px",
    color: "#00182F",
    paddingBottom: "5px",
  },
}))

const ParametersFields = ({
  parameters,
  setParameters,
  errors,
  setErrors,
  smsDuration,
  setSmsDuration,
}) => {
  const classes = useStyles()

  const handleParameterChange = (event) => {
    let { name, value } = event.target
    if (name == "eventDurationMinutes" || name == "maxRepetitions") {
      value = parseInt(value)
    }
    if (name == "speed") {
      value = parseInt(value)
    }
    setParameters((prevParameters) => ({
      ...prevParameters,
      [name]: value,
    }))
  }

  const handleChangeSpeedArrows = (value) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      SmsDurationError: "",
    }))
    if (smsDuration + value < 3) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SmsDurationError:
          "La duración de los SMS no puede ser menor a 3 segundos",
      }))
    }
    if (smsDuration + value > 15) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SmsDurationError:
          "La duración de los SMS no puede ser mayor a 15 segundos",
      }))
    }
    setSmsDuration((prev) => prev + parseInt(value))
  }

  const handleSpeedChange = (event) => {
    const { value } = event.target
    setErrors((prevErrors) => ({
      ...prevErrors,
      SmsDurationError: "",
    }))
    if (value < 3 || value == "" || value == null) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SmsDurationError:
          "La duración de los SMS no puede ser menor a 3 segundos",
      }))
    }
    if (value > 15) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        SmsDurationError:
          "La duración de los SMS no puede ser mayor a 15 segundos",
      }))
    }
    setSmsDuration(value == "" || value == null ? 0 : parseInt(value))
  }

  const handleEventDurationChange = (event) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      TotalDurationError: "",
    }))
    if (event.target.value < 1) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        TotalDurationError: "La duración no puede ser menor a 1 minuto",
      }))
    }
    handleParameterChange(event)
  }

  const handleEventDurationArrows = (value) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      TotalDurationError: "",
    }))
    if ((parseInt(parameters.eventDurationMinutes) || 0) + value < 1) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        TotalDurationError: "La duración no puede ser menor a 1 minuto",
      }))
    }
    setParameters((prevParameters) => ({
      ...prevParameters,
      eventDurationMinutes:
        (parseInt(prevParameters.eventDurationMinutes) || 0) + value,
    }))
  }

  const handleRepetitionsChange = (event) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      RepetitionsError: "",
    }))
    if (event.target.value < 1) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        RepetitionsError: "La cantidad de repeticiones no puede ser menor a 1",
      }))
    }
    handleParameterChange(event)
  }

  const handleRepetitionsArrows = (value) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      RepetitionsError: "",
    }))
    if ((parseInt(parameters.maxRepetitions) || 0) + value < 1) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        RepetitionsError: "La cantidad de repeticiones no puede ser menor a 1",
      }))
    }
    setParameters((prevParameters) => ({
      ...prevParameters,
      maxRepetitions: (parseInt(prevParameters.maxRepetitions) || 0) + value,
    }))
  }

  return (
    <Grid
      container
      direction={"row"}
      gap={"35px"}
      width={"fit-content"}
      wrap="nowrap"
    >
      <Grid
        item
        container
        direction={"column"}
        width={"fit-content"}
        gap={"18px"}
      >
        <div>
          <Typography className={classes.labelInput}>
            Cintillo a utilizar:
          </Typography>
          <select
            name="model"
            id="ticker-model"
            className={classes.parameterInput + " " + classes.selectInput}
            style={{ width: "236px" }}
            value={parameters.model || ""}
            onChange={handleParameterChange}
          >
            {Object.keys(tickerModels).map((model) => (
              <option key={model} value={model}>
                {model.split("_")[0].charAt(0).toUpperCase() +
                  model.split("_")[0].slice(1).toLowerCase()}
              </option>
            ))}
          </select>
        </div>

        <div>
          <Typography className={classes.labelInput}>
            Duración total del evento:
          </Typography>
          <CompoundInputNumber
            name="eventDurationMinutes"
            id="event-duration"
            error={errors.TotalDurationError != ""}
            onChange={handleEventDurationChange}
            value={parseInt(parameters.eventDurationMinutes) || 0}
            upArrowAction={() => handleEventDurationArrows(1)}
            downArrowAction={() => handleEventDurationArrows(-1)}
            middleText="Minutos"
          />
          {errors.TotalDurationError != "" ? (
            <Typography
              sx={{
                fontSize: "10px",
                fontFamily: "Inter",
                fontWeight: "600",
                color: "red",
                width: "236px",
                marginBottom: "0px",
                marginTop: "0px",
              }}
            >
              {errors.TotalDurationError}
            </Typography>
          ) : null}
        </div>

        <div>
          <Typography className={classes.labelInput}>
            Hora de cierre del evento:
          </Typography>
          <input
            type="time"
            name="closeHour"
            id="close-hour"
            className={classes.parameterInput}
            style={{ padding: "0 10px 0 45px", width: "236px" }}
            value={parameters.closeHour || ""}
            onChange={handleParameterChange}
          />
        </div>

        <div>
          <Typography className={classes.labelInput}>
            Repeticiones por SMS:
          </Typography>
          <CompoundInputNumber
            name="maxRepetitions"
            id="sms-repetitions"
            error={errors.RepetitionsError != ""}
            onChange={handleRepetitionsChange}
            value={parseInt(parameters.maxRepetitions) || 0}
            upArrowAction={() => handleRepetitionsArrows(1)}
            downArrowAction={() => handleRepetitionsArrows(-1)}
            middleText="Veces"
          />
          {errors.RepetitionsError != "" ? (
            <Typography
              sx={{
                fontSize: "10px",
                fontFamily: "Inter",
                fontWeight: "600",
                color: "red",
                width: "236px",
              }}
            >
              {errors.RepetitionsError}
            </Typography>
          ) : null}
        </div>
      </Grid>
      <Grid
        item
        container
        direction={"column"}
        width={"fit-content"}
        gap={"18px"}
      >
        <div>
          <Typography className={classes.labelInput}>
            Tamaño del cintillo:
          </Typography>
          <select
            name="size"
            id="ticker-size"
            className={classes.parameterInput + " " + classes.selectInput}
            style={{ width: "236px" }}
            value={parameters.size || ""}
            onChange={handleParameterChange}
          >
            {Object.keys(tickerSizes).map((size) => (
              <option key={size} value={size}>
                {TickerSizePipe({ size })}
              </option>
            ))}
          </select>
        </div>
        <div>
          <Typography className={classes.labelInput}>
            Separador de SMS:
          </Typography>
          <FileInputManagment
            parameters={parameters}
            setParameters={setParameters}
            errors={errors}
            setErrors={setErrors}
          />
        </div>

        <div>
          <Typography className={classes.labelInput}>
            Duración de SMS en pantalla:
          </Typography>
          <CompoundInputNumber
            name="speed"
            id="speed"
            onChange={handleSpeedChange}
            error={errors.SmsDurationError != ""}
            value={smsDuration}
            upArrowAction={() => {
              handleChangeSpeedArrows(1)
            }}
            downArrowAction={() => {
              handleChangeSpeedArrows(-1)
            }}
            middleText="Segundos"
          />
          {errors.SmsDurationError != "" ? (
            <Typography
              sx={{
                fontSize: "10px",
                fontFamily: "Inter",
                fontWeight: "600",
                color: "red",
                width: "236px",
              }}
            >
              {errors.SmsDurationError}
            </Typography>
          ) : null}
        </div>
      </Grid>
    </Grid>
  )
}

ParametersFields.propTypes = {
  parameters: PropTypes.object.isRequired,
  setParameters: PropTypes.func.isRequired,
  errors: PropTypes.object.isRequired,
  setErrors: PropTypes.func.isRequired,
  smsDuration: PropTypes.number.isRequired,
  setSmsDuration: PropTypes.func.isRequired,
}

export default ParametersFields
