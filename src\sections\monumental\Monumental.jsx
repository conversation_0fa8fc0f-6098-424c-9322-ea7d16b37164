import React, { useState, useEffect } from "react"
import { But<PERSON>, createSvgIcon, Grid } from "@mui/material"
import { makeStyles } from "@mui/styles"
import HeadSectionComponent from "../../components/SectionTable/HeadSection"
import { useSocket } from "../../custom-hooks/socket-hook"
import TodayMessages from "./components/todayMessages/todayMessages"
import AllMessages from "./components/allMessages"
import AllApprovedMessages from "./components/allApprovedMessages"
import { useSearchParams } from "react-router-dom"
import { MonumentalMessagesSocketEvents } from "./socket-events/monumental-socket-events"
import MessageQuantity from "./components/messageQuantity"
import InappropiateMessages from "./components/inappropiateMessages"


const PendingCampaignIcon = createSvgIcon(
  <svg
    width="18"
    height="32"
    viewBox="0 0 18 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="9" cy="12" r="9" fill="currentColor" />
    <path
      d="M9.02011 14.094C8.45611 14.094 8.14411 13.764 8.08411 13.104L7.50811 6.948C7.44811 6.42 7.55011 5.988 7.81411 5.652C8.09011 5.316 8.49211 5.148 9.02011 5.148C9.53611 5.148 9.92611 5.316 10.1901 5.652C10.4541 5.988 10.5561 6.42 10.4961 6.948L9.92011 13.104C9.84811 13.764 9.54811 14.094 9.02011 14.094ZM9.02011 18.09C8.54011 18.09 8.15611 17.946 7.86811 17.658C7.59211 17.37 7.45411 16.998 7.45411 16.542C7.45411 16.098 7.59211 15.738 7.86811 15.462C8.15611 15.174 8.54011 15.03 9.02011 15.03C9.50011 15.03 9.87211 15.174 10.1361 15.462C10.4121 15.738 10.5501 16.098 10.5501 16.542C10.5501 16.998 10.4121 17.37 10.1361 17.658C9.87211 17.946 9.50011 18.09 9.02011 18.09Z"
      fill="white"
    />
  </svg>,
  "pending_campaign_icon"
)

const useStyles = makeStyles(() => ({
  campaignMenuItem: {
    height: 32,
    textAlign: "center !important",
    "&:hover": {
      background: "rgba(9, 87, 160, 0.15) !important",
      color: "#0957A0",
    },
  },
  campaignMenuItemText: {
    fontSize: 12,
    fontFamily: "Inter",
    color: "inherit",
  },
  lookupOption: {
    color: "rgba(78, 85, 137, 0.30) !important",
    border: "1px solid  rgba(78, 85, 137, 0.30) !important",
    borderRadius: "10px !important",
    textTransform: "none !important",
    width: "225px",
    height: "29px",
    padding: "3px 7px 4px 7px !important",
    fontFamily: "Inter !important",
    fontSize: "16px !important",
    fontStyle: "normal !important",
    fontWeight: "700 !important",
    lineHeight: "normal !important",
    gap: "0px",
  },
  activeOption: {
    color: "#363636 !important",
    border: "1px solid #363636 !important",
  },
  containerWithFilters: {
    marginBottom: "0px !important",
    marginLeft: "22px !important",
  },
}))

const Monumental = () => {
  const classes = useStyles()

  const [lookupOptions] = useState([
    { label: "Mensajes para hoy" },
    { label: "Mensajes publicados" },
    { label: "Todos los mensajes" },
    { label: "Mensajes inapropiados" },
  ])

  const [actualOption, setActualOption] = useState(0)
  const [searchParams, setSearchParams] = useSearchParams()

  const [totalMessages, setTotalMessages] = useState({
    innapropiateCount: 0,
    newCount: 0,
    approvedCount: 0,
  })
  const [changed, setChanged] = useState(0)

  const { socket, on, emit, connected, off } = useSocket(
    `${process.env.REACT_APP_BACKEND_URL}`,
    {
      path: `${process.env.REACT_APP_MONUMENTAL_WS_PATH}`,
      secure: true,
      withCredentials: true,
      transports: ["websocket"],
      extraHeaders: {
        [`${process.env.REACT_APP_WS_SECRET_HEADER}`]: `${process.env.REACT_APP_WS_SECRET_HEADER_VALUE}`,
      },
      auth: {
        [`${process.env.REACT_APP_WS_SECRET_HEADER}`]: `${process.env.REACT_APP_WS_SECRET_HEADER_VALUE}`,
      },
    }
  )

  useEffect(() => {
    if (!socket) return

    // Listen for connection
    on("connect", () => {
      console.log("Connected to socket server")
    })
    on(MonumentalMessagesSocketEvents.SEND_TOTAL_MESSAGE_QUANTITY, (data) => {
      setTotalMessages((prev) => {
        if (prev.newCount < data.newCount)
          setChanged(prev => prev + 1)
        return data
      })
    })

    return () => {
      off("connect")
      off(MonumentalMessagesSocketEvents.SEND_TOTAL_MESSAGE_QUANTITY)
    }
  }, [socket, on, off])

  useEffect(() => {
    emit(MonumentalMessagesSocketEvents.REQUEST_TOTAL_MESSAGE_QUANTITY)
    const window = searchParams.get("window")
    if (window) {
      setActualOption(parseInt(window))
    }
  }, [])

  return (
    <React.Fragment>
      <HeadSectionComponent
        title={"SMS Monumental"}
        subTitle={
          "<p> Gestiona los <b> mensajes SMS </b> que se visualizarán en el <b> evento </b></p>"
        }
        tooltipTitle={"Solicitar campaña"}
        showAddButton={false}
        onAddButtonClick={() => {}}
        customStyle={{ container: classes.containerWithFilters }}
      />
      { (totalMessages.innapropiateCount != 0 || totalMessages.newCount != 0 || totalMessages.approvedCount != 0) &&
      <MessageQuantity
        innapropiateQuantity={parseInt(totalMessages.innapropiateCount)}
        newQuantity={parseInt(totalMessages.newCount)}
        approvedQuantity={parseInt(totalMessages.approvedCount)}
        changed={changed}
      />}
      <Grid
        container
        gap={"30px"}
        flexWrap="nowrap"
        justifyContent="start"
        padding={"39px 0px 0px 0px"}
        marginLeft={"22px !important"}
        maxHeight={"1px"}
      >
        {lookupOptions.map((option, ind) => (
          <Grid item key={`LO-${ind}`}>
            <Button
              onClick={() => {
                setActualOption(ind)
              }}
              variant="outlined"
              className={`${classes.lookupOption} ${
                actualOption !== null && actualOption === ind
                  ? classes.activeOption
                  : ""
              }`}
            >
              {ind === 3 && (
                <PendingCampaignIcon
                  style={{ color: "#ee2029", margin: 0 }}
                />
              )}
              {option.label}
            </Button>
          </Grid>
        ))}
      </Grid>
      {actualOption == 0 ? (
        <TodayMessages socket={socket} on={on} off={off} emit={emit} />
      ) : (
        <React.Fragment />
      )}
      {actualOption == 1 ? <AllApprovedMessages /> : <React.Fragment />}
      {actualOption == 2 ? <AllMessages /> : <React.Fragment />}
      {actualOption == 3 ? <InappropiateMessages socket={socket} on={on} off={off} emit={emit} inappropiateQuantity={parseInt(totalMessages.innapropiateCount)}/> : <React.Fragment />}
    </React.Fragment>
  )
}

export default Monumental
