import { useState, useEffect, useRef } from "react"
import { WidgetInstance } from "friendly-challenge"
import {
  <PERSON>rid,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Fade,
  Box,
} from "@mui/material"
import { makeStyles } from "@mui/styles"
import { useNavigate } from "react-router"
import { useForm } from "react-hook-form"
import { useApp } from "../../AppProvider"
import { login } from "./services/api"
import { Visibility, VisibilityOff } from "@mui/icons-material"
import logoStock from "./../../assets/ConectiumFullLogo.svg"
import monumentalLogo from "./../../assets/monumentalLogo.png"
import fondoLogin from "./../../assets/fondo-login-monumental.PNG"
import { email_regex } from "../../helpers/regular_expressions"
import "./index.css"
import PrimaryButton from "../../components/PrimaryButton"
import {ROLES} from "../../sections/sidebar/constants"

const useStyles = makeStyles(() => ({
  root: {
    height: "calc(100vh - 180px)",
    backgroundImage: `url(${fondoLogin})`,
    fontFamily: "Inter, sans-serif",
    padding: "90px 0",
    overflowY: "hidden",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
  loginIcon: {
    width: "100%",
    height: "100%",
    background: "rgba(6 153 226)",
    objectFit: "cover",
  },
  phoneImage: {
    position: "absolute",
    height: "80%",
    marginRight: "8%",
  },
  titleText: {
    color: "#F5F5F5",
    margin: 0,
    fontWeight: "400",
    fontFamily: "Inter, sans-serif",
    fontSize: "35px",
    "& b": {
      color: "#009EFF",
      fontWeight: "700",
    },
    "& strong": {
      color: "#F5F5F5",
      margin: 0,
      fontWeight: "700",
      fontFamily: "Inter, sans-serif",
      fontSize: "35px",
    },
    ["@media (max-width: 1370px)"]: {
      fontSize: 30,
    },
  },

  baseFrame: {
    minHeight: "545px !important",
    maxHeight: "545px !important",
    width: "555px !important",
    background: "rgba(231, 231, 231, 0.20)",
    borderRadius: "20px"
  },
  formInput: {
    "& .MuiOutlinedInput-root": {
      "& input:-webkit-autofill": {
        "-webkit-text-fill-color": "white",
        "-webkit-box-shadow": "0 0 0px 1000px transparent inset",
        transition: "background-color 5000s ease-in-out 0s",
      },
      "& input::placeholder": {
        color: "white",
        opacity: 1,
      },
      "& fieldset": {
        borderRadius: "8px",
        borderColor: "white",
      },
      "&:hover fieldset": {
        borderColor: "white !important",
      },
      "&.Mui-focused fieldset": {
        borderColor: "white",
      },
      "&.Mui-error fieldset": {
        borderColor: "white",
      },
    },
    "& .MuiInputBase-root": {
      height: "50px !important",
      width: "373px !important",
    },
    "& .MuiInputPlaceholder-root": {
      color: "white",
      fontFamily: "Inter",
      fontSize: "16px !Important",
      fontWeight: "500",
    },
    "& .Mui-focused": {
      color: "white !important",
    },
    "& .Mui-error": {
      color: "#d32f2f !important",
    },
  },
  ConectiumLogo: {
    width: "308px",
    height: "94px",
  },
  monumentalLogo: {
    width: "221px",
    height: "72.28px",
  },
  captchaContainer: {
    height: "60px !important",
    background: "white !important",
    width: "320px !important",
  },

}))

const Login = () => {
  const navigate = useNavigate()
  const classes = useStyles()
  const { setCurrentUser, showNotification } = useApp()
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()
  const { email: emailError, password: passwordError } = errors
  const [showPassword, setShowPassword] = useState(false)
  const [captchaSolved, setCaptchaSolved] = useState(false)
  const captchaRef = useRef(null)
  const widgetRef = useRef(null)

  useEffect(() => {
    if (captchaRef.current && !widgetRef.current) {
      widgetRef.current = new WidgetInstance(captchaRef.current, {
        startMode: "auto",
        doneCallback: (solution) => {
          setCaptchaSolved(true)
        },
        errorCallback: (err) => {
          console.error("Captcha error:", err)
        },
      })
    }

    return () => {
      if (widgetRef.current) {
        widgetRef.current.destroy()
      }
    }
  }, [])

  const onSubmit = async ({ email, password }) => {
    try {
      if (!captchaSolved) {
        showNotification("Please complete the CAPTCHA", "error")
        return
      }
      const response = await login(email, password)
      localStorage.setItem(
        "conectium-monumental-admin-token",
        response.data.access_token
      )
      localStorage.setItem(
        "current-monumental-user",
        JSON.stringify(response.data.user)
      )
      setCurrentUser(response.data.user)
      console.log("Usuario logueado:", response.data.user)

      if(response.data.user.role === ROLES.TRIVIA_ADMIN) {
         navigate("/trivia")
      }else {
           navigate("/")
      }
   
    } catch (err) {
      if (err.response) {
        switch (err.response.status) {
          case 403:
            showNotification(
              "Tu usuario no ha sido verificado. Comunícate con el administrador para crear tus credenciales de acceso",
              "error"
            )
            break
          case 406:
            showNotification(
              "Lo sentimos, su usuario no tiene acceso. Por favor, comuníquese con el administrador.",
              "error"
            )
            break
          default:
            showNotification(
              "Lo sentimos, la combinación de correo electrónico y contraseña no es válida",
              "error"
            )
            break
        }
      } else {
        showNotification(
          "Lo sentimos, ha ocurrido un error inesperado",
          "error"
        )
      }
    }
  }

  const greenButton = {
    borderColor: "#4E5589",
    textTransform: "none",
    borderWidth: "2px",
    borderRadius: "8px",
    color: "#4E5589",
    height: "53px",
    "&:hover": {
      /* background: " linear-gradient( #008C0D 33.74%, #00BF19 96.06%);", */
      background: "#4E5589",
      boxShadow:
        "0px 1px 2px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.12), 0px 1px 8px rgba(0, 0, 0, 0.1)",
      color: "white",
      borderRadius: "8px",
    },
  }

  return (
    <Box className={classes.root}>
      <Grid item textAlign="center" width="25vw" margin={"0 0 46px 0"}>
        <img
          alt="andromeda"
          src={logoStock}
          className={classes.conectiumLogo}
        />
      </Grid>
      <Grid
        item
        container
        className={classes.baseFrame}
        direction="column"
        alignItems="center"
        justifyContent={"start"}
        padding="11px 0 0 0"
        margin={"0 0 71px 0"}
      >
        <Grid
          item
          width="25vw"
          style={{
            textAlign: "center",
            marginBottom: "2%",
            letterSpacing: "0.15px",
            fontFamily: "Inter",
            color: "white",
            fontSize: "24px",
            fontWeight: "600",
            lineHeight: "24px",
          }}
        >
          <p>Iniciar sesión</p>
        </Grid>
        <Grid item alignItems="center">
          <form onSubmit={handleSubmit(onSubmit)} style={{ width: "25vw" }}>
            <Grid
              item
              container
              direction="column"
              alignItems="center"
              spacing={2}
            >
              <Grid item container justifyContent="center">
                <TextField
                  className={classes.formInput}
                  inputProps={{
                    style: {
                      background: "transparent",
                      textAlign: "center",
                      "&::placeholder": {
                        color: "white",
                      },
                      color: "white",
                    },
                  }}
                  sx={{
                    marginBottom: "25px !important",
                  }}
                  id="email"
                  placeholder="Correo electrónico"
                  variant="outlined"
                  helperText={emailError?.message}
                  error={emailError !== undefined}
                  {...register("email", {
                    required: {
                      value: true,
                      message: "Campo obligatorio",
                    },
                    pattern: {
                      value: email_regex,
                      message: "Correo electrónico inválido",
                    },
                    validate: {
                      rangeLength: (value) =>
                        value.split("@")[0].length <= 64 ||
                        "Correo electrónico inválido",
                    },
                  })}
                />
              </Grid>
              <Grid item container justifyContent="center">
                <TextField
                  className={classes.formInput}
                  id="password"
                  placeholder="Contraseña"
                  type={showPassword === false ? "password" : "text"}
                  variant="outlined"
                  helperText={passwordError?.message}
                  error={passwordError !== undefined}
                  InputProps={{
                    style: {
                      textAlign: "center",
                      background: "transparent",
                    },
                    inputProps: {
                      style: {
                        textAlign: "center",
                        "&::placeholder": {
                          color: "white",
                        },
                        color: "white",
                      },
                    },
                    endAdornment: (
                      <InputAdornment position="end" sx={{ left: "-15px" }}>
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                          sx={{
                            padding: "8px",
                            "&:hover": {
                              background: "transparent",
                            },
                          }}
                        >
                          {showPassword ? (
                            <Visibility
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            />
                          ) : (
                            <VisibilityOff
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  {...register("password", {
                    required: {
                      value: true,
                      message: "Campo obligatorio",
                    },
                    maxLength: {
                      value: 15,
                      message:
                        "La contraseña supera el límite de caracteres permitidos",
                    },
                    minLength: {
                      value: 6,
                      message:
                        "La contraseña no cumple con el mínimo de caracteres",
                    },
                  })}
                />
              </Grid>
              <Grid item container justifyContent="center" width="373px">
                <Typography
                  sx={{
                    fontFamily: "Inter, sans-serif",
                    fontWeight: "600",
                    fontSize: "12px",
                    lineHeight: "24px",
                    letterSpacing: "0.15px",
                    color: "white",
                    marginBottom: "19px",
                    "&:hover": {
                      cursor: "pointer",
                    },
                  }}
                  onClick={() => {
                    navigate("/recover-password")
                  }}
                >
                  ¿Olvidaste tu contraseña?
                </Typography>
              </Grid>
              <Grid
                item
                container
                justifyContent="center"
                margin={"0 0 30px 0"}
              >
                <PrimaryButton
                  width="373px"
                  height="53px"
                  action={() => {}}
                  text="Ingresar"
                  fontSize="20"
                  type="submit"
                  cs={{
                    borderRadius: "25px",
                  }}
                ></PrimaryButton>
              </Grid>
              <Grid item container alignItems="center" direction={"column"} >
                <div
                  ref={captchaRef}
                  className={classes.captchaContainer}
                  data-sitekey={process.env.REACT_APP_RECAPTCHA_SITE_KEY}
                  data-start="auto"
                />
                {!captchaSolved && (
                  <Typography
                    sx={{
                      fontSize: "12px",
                      fontFamily: "Inter",
                      fontWeight: "300",
                      lineHeight: "24px",
                      letterSpacing: "0.15px",
                      color: "red",
                    }}
                  >
                    Por favor completa el CAPTCHA
                  </Typography>
                )}
              </Grid>
            </Grid>
          </form>
        </Grid>
      </Grid>
      <Grid item>
        <img
          alt="andromeda"
          src={monumentalLogo}
          className={classes.monumentalLogo}
        />
      </Grid>
    </Box>
  )
}

export default Login
