import React, { useState, useEffect, useRef, useCallback } from "react"
import { MonumentalTickerSocketEvents } from "../socket-events/monumental-ticker-socket-event"
import { debounce } from "lodash"

export const useCheckFetchNewMessage = (
  emit,
  tickerRef,
  containerRef,
  setFetchMessages,
  animation,
  deleteFirstMessage,
  resetMessages
) => {

  useEffect(() => {
    let raf
    function checkMessagesInView() {
      if (animation && animation.playState === "running") {
        if (!tickerRef.current || !containerRef.current) return
        const containerRect = containerRef.current.getBoundingClientRect()
        const items = tickerRef.current.children
        if (items.length === 0) return
        const item = items[items.length - 1]
        const firstItem = items[0]
        const itemRect = item.getBoundingClientRect()
        if (
          itemRect.right - 50 <= containerRect.right
        ) {
          fetchMessage(items)
        }
        if (
          item.getBoundingClientRect().right + 150 <
          containerRect.left
        ) {
          resetMessages()
        }
        if (
          firstItem.getBoundingClientRect().right + 150 <
          containerRect.left
        ) {
          deleteFirstMessage()
        }
      }
      raf = requestAnimationFrame(checkMessagesInView)
    }
    raf = requestAnimationFrame(checkMessagesInView)
    return () => cancelAnimationFrame(raf)
  }, [emit, animation])

  const fetchMessage = debounce((items) => {
    setFetchMessages((prev) => prev + 1)
  }, 0)

  return {}
}
