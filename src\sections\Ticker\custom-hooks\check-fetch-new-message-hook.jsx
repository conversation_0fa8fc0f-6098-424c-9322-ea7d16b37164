import React, { useState, useEffect, useRef, useCallback } from "react"
import { MonumentalTickerSocketEvents } from "../socket-events/monumental-ticker-socket-event"
import { debounce } from "lodash"

export const useCheckFetchNewMessage = (
  emit,
  tickerRef,
  containerRef,
  calculateAndSetAnimation,
  messagesItems,
  speed,
  isShutdown,
  setFetchMessages
) => {
  const [initialRun, setInitialRun] = useState(true)
  const enteredMessagesRef = useRef(new Set())
  const enteredMessagesRefHistoric = useRef(new Set())
  const leftMessagesRefHistoric = useRef(new Set())
  const [receivedMessage, setReceivedMessage] = useState(false)

  useEffect(() => {
    let raf
    function checkMessagesInView() {
      if (!tickerRef.current || !containerRef.current) return
      const containerRect = containerRef.current.getBoundingClientRect()
      const items = tickerRef.current.children
      if (items.length === 0) return
      const item = items[items.length - 1]
      const itemRect = item.getBoundingClientRect()
      if (
        itemRect.right - 50 <= containerRect.right &&
        !receivedMessage
      ) {
        fetchMessage(items)
      }

      raf = requestAnimationFrame(checkMessagesInView)
    }
    raf = requestAnimationFrame(checkMessagesInView)
    return () => cancelAnimationFrame(raf)
  }, [messagesItems, emit])

  useEffect(() => {
    if (initialRun) {
      setInitialRun(false)
      return
    }
    setReceivedMessage(true)
    if (!tickerRef.current) return
    enteredMessagesRef.current = new Set()
    const items = tickerRef.current.children
    calculateAndSetAnimation()
  }, [messagesItems, speed])

  useEffect(() => {
    setReceivedMessage(false)
    enteredMessagesRef.current = new Set()
    enteredMessagesRefHistoric.current = new Set()
    leftMessagesRefHistoric.current = new Set()
  }, [isShutdown])

  const fetchMessage = debounce((items) => {
    enteredMessagesRef.current.add(items.length - 1)
    if (!enteredMessagesRefHistoric.current.has(items.length - 1)) {
      enteredMessagesRefHistoric.current.add(items.length - 1)
    }
    setReceivedMessage(false)
    setFetchMessages((prev) => prev + 1)
  }, 0)

  return {
    enteredMessagesRef,
    enteredMessagesRefHistoric,
    leftMessagesRefHistoric,
  }
}
