import React, { useState, useEffect, useRef, useCallback } from "react"
import { MonumentalTickerSocketEvents } from "../socket-events/monumental-ticker-socket-event"
import { debounce } from "lodash"

export const useCheckFetchNewMessage = (
  emit,
  tickerRef,
  containerRef,
  calculateAndSetAnimation,
  messagesItems,
  animationKey,
  speed,
  isShutdown,
  setMessageOffsett
) => {
  const [initialRun, setInitialRun] = useState(true)
  const enteredMessagesRef = useRef(new Set())
  const enteredMessagesRefHistoric = useRef(new Set())
  const leftMessagesRefHistoric = useRef(new Set())
  const [receivedMessage, setReceivedMessage] = useState(false)
  const [receivedAfter, setReceivedAfter] = useState(false)
  useEffect(() => {
    let raf
    function checkMessagesInView() {
      if (!tickerRef.current || !containerRef.current) return
      const containerRect = containerRef.current.getBoundingClientRect()
      const items = tickerRef.current.children
      if (items.length === 0) return
      const item = items[items.length - 1]
      const itemRect = item.getBoundingClientRect()
      if (
        itemRect.right - 50 <= containerRect.right &&
        itemRect.right - 50 >= containerRect.right - 20 &&
        !receivedMessage
      ) {
        fetchMessage(items, containerRect.right - (itemRect.right - 50))
      }

      raf = requestAnimationFrame(checkMessagesInView)
    }
    raf = requestAnimationFrame(checkMessagesInView)
    return () => cancelAnimationFrame(raf)
  }, [messagesItems, emit])

  useEffect(() => {
    if (initialRun) {
      setInitialRun(false)
      return
    }
    setReceivedMessage(true)
    if (!tickerRef.current) return
    enteredMessagesRef.current = new Set()
    const items = tickerRef.current.children
    for (let i = 0; i < items.length; i++) {
      
      if (leftMessagesRefHistoric.current.has(i)) {
        calculateAndSetAnimation(
          containerRef.current.offsetWidth,
          i,
          null,
          null,
          null,
          messagesItems[i].offset
        )
      } else {
        if (enteredMessagesRefHistoric.current.has(i)) {
          calculateAndSetAnimation(
            containerRef.current.offsetWidth,
            i,
            null,
            true,
            i == items.length - 1 ? receivedAfter : null,
            messagesItems[i].offset
          )
        } else
          calculateAndSetAnimation(
            containerRef.current.offsetWidth,
            i,
            i == items.length - 1 && items.length > 2 ? i : null,
            false,
            i == items.length - 1 ? receivedAfter : null,
            messagesItems[i].offset
          )
      }
    }
    setReceivedAfter(false)
  }, [messagesItems, animationKey, speed])

  useEffect(() => {
    setReceivedAfter(false)
    setReceivedMessage(false)
    enteredMessagesRef.current = new Set()
    enteredMessagesRefHistoric.current = new Set()
    leftMessagesRefHistoric.current = new Set()
  }, [isShutdown])

  const fetchMessage = debounce((items, offset) => {
    enteredMessagesRef.current.add(items.length - 1)
    if (!enteredMessagesRefHistoric.current.has(items.length - 1)) {
      enteredMessagesRefHistoric.current.add(items.length - 1)
    }
    setReceivedMessage(false)
    setMessageOffsett((prev) => offset)
    setReceivedAfter(true)
  }, 0)

  return {
    enteredMessagesRef,
    enteredMessagesRefHistoric,
    leftMessagesRefHistoric,
  }
}
