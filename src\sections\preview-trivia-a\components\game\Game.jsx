import React, { useState, useEffect, useRef, useCallback } from 'react';
import fondo from "../../../../assets/side-fondo.png"; 
import simon from "../../../../assets/simon2.png"; 
import tribu from "../../../../assets/logoAqustico.png";
import tribuPromo from "../../../../assets/aq-promo.svg";
import tribuCom from "../../../../assets/trivia-com.png";
import com from "../../../../assets/com.png";
import { useParams, useNavigate } from "react-router-dom";
import { io } from 'socket.io-client';

import PropTypes from "prop-types"
import { setWinners ,completeTriviaById, setTimer} from '../../../trivia/services/api';
/* import { getTriviaById } from "../../services/trivaService";
 */

import {TriviaSocketEvents } from '../../../../sections/trivia/socket-events/trivia-socket-events';
import { useSocket } from '../../../../contexts/SocketContext';
import styles from './Game.module.css';
import { duration } from 'moment';

const SOCKET_SERVER_URL = process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH; // La URL de tu backend NestJS (donde escucha el servidor de Nest)
const ASSETS_URL = process.env.REACT_APP_BACKEND_URL; // La URL de tus assets, si es diferente
const GameAQ = ({ campaign }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [completed, setCompleted] = useState(false);



 const [connectionStatus, setConnectionStatus] = useState('Conectando...');

  const [progressValue, setProgressValue] = useState(0);
  const [timerValue, setTimerValue] = useState(10);
  const [testCompleted, setTestCompleted] = useState(false);
  const [showPromotion, setShowPromotion] = useState(false);
  const [showedPromotion, setShowedPromotion] = useState(false); // Flag to track if promotion has been shown
  const [final, setFinal] = useState(false); // Indicates if all questions are completed
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(5);

  const [questions, setQuestions] = useState(campaign.questions || []); // Assuming campaign has a questions array

  const activeQuestion = questions[currentQuestionIndex];

  const intervalRef = useRef(null);
    // Ref to store the timeout IDs
  const timeoutRef = useRef([]);

  const socket = useSocket();

   useEffect(() => {
    const socket = io(SOCKET_SERVER_URL);

    socket.on(TriviaSocketEvents.NEXT_QUESTION, (data) => {
      if(data.id !== id) return; // Ensure the data is for the current trivia
        setLoading(false);
        console.log('Evento "NEXT_QUESTION" recibido:', data);
        if (data.promotion) {
            console.log('Promoción recibida:', data);
            setShowPromotion(true); // Mostrar promoción
            const promotionDuration = data.duration || 10; // Duración de la publicidad, con un valor predeterminado
            startProgressBar(promotionDuration);
        } else {
            setShowPromotion(false); // Ocultar promoción al recibir una nueva pregunta
            const { id, position, questionId, question, answers } = data;

            // Actualizar la pregunta activa
            setQuestions((prevQuestions) => {
                const updatedQuestions = [...prevQuestions];
                updatedQuestions[position - 1] = {
                    id: questionId,
                    position: position,
                    duration: data.duration,
                    question,
                    answers: answers.map(answer => ({
                        text: answer.text,
                        id: answer.id,
                        correct: answer.correct,
                    })),
                };
                return updatedQuestions;
            });
            setCurrentQuestionIndex(position - 1); // Ajustar el índice de la pregunta actual
        }
    });


    socket.on(TriviaSocketEvents.PAUSE_TRIVIA, (data) => {
      console.log('Evento "PAUSE" recibido:', data);
      if (id === data.id) {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            /* setTimer({ id, timerValue: currentTime / 1000 });  */// Enviar el valor actual del temporizador
        }
      }
        
    });

    socket.on(TriviaSocketEvents.RESUME_TRIVIA, (data) => {
      console.log('Evento "RESUME" recibido:', data);
        if (id === data.id) {
            startProgressBar(data.duration); // Reanudar el temporizador desde el valor actual
        }
    });

    socket.on(TriviaSocketEvents.FINISH_TRIVIA, (data) => {
        if (data.id === id) {
            navigate(`/previewaq/${id}/final`);
        }
    });

    return () => {
        socket.off(TriviaSocketEvents.NEXT_QUESTION);
        socket.off(TriviaSocketEvents.FINISH_TRIVIA);
        socket.off(TriviaSocketEvents.PAUSE_TRIVIA);
        socket.off(TriviaSocketEvents.RESUME_TRIVIA);
        socket.off(TriviaSocketEvents.SEND_TIMER_VALUE);
        socket.disconnect();
    };
}, []); // Eliminar `questions` de las dependencias

  const clearAllTimeouts = () => {
    timeoutRef.current.forEach(id => clearTimeout(id));
    timeoutRef.current = [];
  };

  // --- startProgressBar equivalent ---
  const startProgressBar = useCallback((duration) => {
    console.log("Starting progress bar with duration:", duration);
    setProgressValue(0);
    setTimerValue(duration);

    // Clear any existing interval before starting a new one
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    const intervalDuration = 1000; // Set interval duration to 1 second

    intervalRef.current = setInterval(() => {
      setTimerValue(prevTimer => {
        if (prevTimer <= 1) {
          clearInterval(intervalRef.current);
          setProgressValue(100); // Ensure progress bar reaches 100% when timer ends
          return 0; // Stop the timer at 0
        }
        return prevTimer - 1;
      });

      setProgressValue(prevProgress => {
        const newProgress = Math.min(prevProgress + (100 / duration), 100);
        return newProgress;
      });
    }, intervalDuration);
  }, []);

  // --- completeTest equivalent ---
  const completeTest = useCallback(() => {
    setTestCompleted(true);
    const timeoutId = setTimeout(() => {
   
    }, 2000);
    timeoutRef.current.push(timeoutId);
  }, []); // Dependencies: nextQuestion


  useEffect(() => {
    if (progressValue === 100) {
      completeTest();
    }
  }, [progressValue, completeTest]);

  useEffect(() => {
    // Start the progress bar initially, or when currentQuestionIndex changes
    if (!final) { // Only start if the quiz isn't finished
      let duration;
      if (questions[currentQuestionIndex].duration) {
        duration = questions[currentQuestionIndex].duration
      } else {
        if(campaign.status === "CREATED") {
           duration = campaign.delay_start_duration;
        }else{
           duration = 0;
        }
        // Fallback to campaign delay if no question is available
      }
      startProgressBar(duration);
    }

    // Cleanup function for useEffect: This runs when the component unmounts
    // or before the effect runs again (if dependencies change).
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      clearAllTimeouts(); // Clear any pending timeouts on unmount
    };
  }, [currentQuestionIndex, final, startProgressBar, campaign.delay_start_duration]);

  useEffect(() => {
    console.log("Conectando al servidor campana...",campaign);
    if (campaign) {
      setLoading(true);
    }
  }, [campaign]);

  useEffect(() => {
    if (!loading && currentQuestionIndex === 0) {
        startProgressBar(campaign.delay_start_duration); // Iniciar el contador al mostrar la primera pregunta
    }
}, [loading]);

  return (
  <React.Fragment>
    {showPromotion ? (
      <div style={{ position: 'absolute', top: 0, left: 0, width: '100vw',
       height: '100vh', zIndex: 9999 }}>
      <div
        className={styles.container}
        style={{ 
        paddingTop: '0px',  }}
      >
          <img src={`${ASSETS_URL}/${campaign.id_publicity.url_web}`} style={{
            width: '100%',
          height: '100%',
          position: 'absolute',
          top: 0,
          zIndex:-1
        }} alt="" />
        <div className={styles.promotionContainer}>
          <div className={styles.promotionOverlay}>
            <div className={styles.promotionContent}>
              <p dangerouslySetInnerHTML={{ __html: campaign.id_publicity.description }}></p>
              <img style={{ width: '12vw' }} src={tribuPromo} alt="" />
              <div style={{ fontSize: '2vh', marginTop: '2vh' }}>Trivia presentada por:</div>
              <img src={tribu} alt="" style={{ width: '15vw' }} />
              <div className={styles.promotionTimer}>
              {timerValue}
            </div>
            </div>
          
          </div>
        </div>
      </div></div>
    ) : (
      <React.Fragment></React.Fragment>
    )}

    <div className={styles.container} style={{ background:'#191F2B' , 
      backgroundImage: `url(${fondo})`,
            backgroundRepeat: "repeat-y",
            backgroundPosition: "left top",
            backgroundSize: "4% 100%",  }}>
       <div  className={styles.violetCircle} style={{top: '-5%', right: '-10%'}}></div>

      <div className={styles.violetCircle2} style={{bottom: '-20%', left: '0%'}}></div>
           
      <div className={styles.header}>
        <img src={tribu} className={styles.headerImage} alt="" />
        <img src={simon} className={styles.headerSimon} alt="" />
      </div>

      <div className={styles.timerContainer}>
        <div className={styles.timer}>
          <div className={styles.timerBox}>0</div>
          <div className={styles.timerBox}>0</div>
          <span className={styles.timerSeparator}>:</span>
          <div className={styles.timerBox}>{timerValue >= 10 ? Math.floor(timerValue / 10) : '0'}</div>
          <div className={styles.timerBox}>{timerValue % 10}</div>
        </div>
      </div>

      {loading ? (
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'column',
            alignItems: 'center',
            fontSize: '5vh',
            fontWeight: 700,
            color: 'white',
            marginTop: '8vh',
          }}
        >
          <p style={{ margin: 0 }}>¡Mantente atent@!</p>
          <p style={{ margin: 0 }}>Nuestra trivia está por comenzar</p>
        </div>
      ) : (
        <div style={{width:'80%', margin:'0 auto'}}>
          <div className={styles.questionContainer}>
            {questions[currentQuestionIndex].position} - {questions[currentQuestionIndex].question || 'Pregunta'}
          </div>
          <div className={styles.answersContainer}>
            <div className={styles.answersGrid}>
              {questions[currentQuestionIndex].answers.map((option, index) => (
                <div key={'index ' + index} className={styles.answerOption}>
                  {option.text || 'Opción'}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

    {/*   <div className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerText}></div>
          <img src={com} style={{ height: '50px', marginTop: '10px' }} alt="" />
        </div>
      </div> */}
    </div>
  </React.Fragment>
);
};

GameAQ.propTypes = {
  campaign: PropTypes.any,
}

export default GameAQ;
