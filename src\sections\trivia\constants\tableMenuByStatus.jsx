import { ROLES } from "../../sidebar/constants";

export const TriviaActionTitle = {
    VERIFY:'Verificar',
    APPROVE:'Aprobar',
    CANCEL:"Cancelar", 
    DETAIL:'Ver detalle',
    STOP:'Pausar',
    COPY:'Crear una copia',
    RESUME:'Reanu<PERSON>',
};

export const triviaStatus = {
    CREATED: 'CREATED',
    STARTED: 'STARTED',
    BEGIN: 'BEGIN',
    ENDED: 'ENDED',
    DELETED: 'DELETED',
    CANCELLED: 'CANCELLED',
    PAUSED: 'PAUSED',
    RESUMED: 'RESUMED',
    FINISHED: 'FINISHED',
    PENDING: 'PENDING',
    IN_PROGRESS: 'IN_PROGRESS',
};


const triviaActions = {
    START: {
        name:'Iniciar',
        modal:true,
        modalConfig: {
            title:'Iniciar Trivia',
            message:() => `¿Estás seguro que deseas iniciar la trivia ?`
        },
        action: () => {},
    },
    RESET: {
        name:'Reiniciar',
        modal:true,
        modalConfig: {
            title:'Reiniciar Trivia',
            message:() => `¿Estás seguro que deseas reiniciar la trivia ?`
        },
        action: () => {}
    },
    EDIT: {
        name:'Editar',
        modal:false,
        action: (campaignId) => `/trivia/update/${campaignId}`,
    },
    STOP: {
        name:'Pausar',
        modal:true,
        modalConfig: {
            title:'Pausar Trivia',
            message:() => `¿Estás seguro que deseas pausar la trivia ?`
        },
        action: () => {},
    },
    RESUME: {
        name:'Reanudar',
        modal:true,
        modalConfig: {
            title:'Reanudar Trivia',
            message:() => `¿Estás seguro que deseas reanudar la trivia ?`
        },
        action: () => {},
    },
    PREVIEW: {
        name:'Preview',
        modal:false,
        action: (campaign) => `/preview/${campaign}`,
    },
}


export const menuActionsByStatus = [
    {
        status: triviaStatus.CREATED,
        displayOptions: [
            triviaActions.START,
            triviaActions.PREVIEW,
            triviaActions.EDIT,
        ],
        disable:{}
    },
    {
        status: triviaStatus.STARTED,
        displayOptions: [
            triviaActions.PREVIEW,
        ],
        disable:{}
    },
    {
        status: triviaStatus.BEGIN,
        displayOptions: [
           
            triviaActions.STOP,
            triviaActions.PREVIEW,
        ],
        disable:{}
    },
    {
        status: triviaStatus.PAUSED,
        displayOptions: [
           
            triviaActions.RESUME,
            triviaActions.PREVIEW,

        ],
        disable:{}
    },
    {
        status: triviaStatus.FINISHED,
        displayOptions: [
            triviaActions.RESET,
            triviaActions.PREVIEW,
            triviaActions.EDIT,

        ],
        disable:{}
    },
    {
        status: triviaStatus.IN_PROGRESS,
        displayOptions: [
            triviaActions.STOP,
            triviaActions.PREVIEW,
        ],
        disable:{}
    },
]