.container {
  width: 100vw;
  min-height: 100vh;
  padding-top: 3vh;
  position: relative;
  box-sizing: border-box;
  z-index: 5;
  background-size: cover;
  font-family: Montserrat;
}

.header {
  width: 70vw;
  height: 15vh;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: auto;
  z-index: 1;
}

.headerImage {
  height: 10vh;
}

.headerSimon {
  height: 11vh;
}

.timerContainer {
  width: 100vw;
  margin-top: 3vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Montserrat;
}

.timer {
  display: flex;
  font-weight: 700;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.timerBox {
  font-size: 10vh;
  
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 65px;
  padding: 0px 6px;
  background: white;
  color: black;
  border-radius: 10px;
}

.timerSeparator {
  display: inline-block;
  width: 15px;
  color: white;
  font-size: 50px;
  text-align: center;
}

.loadingContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  font-size: 5vh;
  font-weight: 700;
  color: white;
  margin-top: 8vh;
}

.questionContainer {
  margin-top: 50px;
  margin-bottom: 30px;
  color: #fff;
  text-align: center;
  font-family: Montserrat;
  font-size: 50px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.answersContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  align-items: center;
  font-family: Montserrat;
}

.answersGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  width: 100%;
  font-family: Montserrat;
}

.answerOption {
  padding: 15px 3px;
  color: #fff;
  border: 2px solid white;
  height: auto;
  text-align: center;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 35px;
  font-weight: 700;
  margin: 0px 0;
  font-family: Montserrat;
}

.footer {
  width: 100%;
  height: 70px;
  background-color: black;
  position: absolute;
  bottom: 0;
  z-index: 2;
  font-family: Montserrat;
}

.footerContent {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2%;
  font-family: Montserrat;
}

.footerText {
  color: #fff;
  text-align: center;
  font-family: Montserrat;
  font-size: 45px;
  font-style: normal;
  font-weight: 400;
  line-height: 70px;
}

.footerImage {
  height: 50px;
  margin-top: 10px;
}

.promotionContainer {
  width: 100vw;
  height: 100vh;
  position: absolute;
  overflow: hidden;
  background-size: 100% 100%;
  z-index: 50;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-family: Montserrat;
}

.promotionOverlay {
  width: 100%;
  height: 100%;
  position: relative;
}

.promotionContent {
  width: 100%;
  height: 100%;
  background: #000000b2;
  padding-top: 8vh;
  z-index: 4;
  color: white;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex-direction: column;
  font-family: Montserrat;
  font-weight: 700;
  font-size:3vh;
  line-height: normal;
  font-style: normal;
}

.promotionTimer {
  z-index: 3;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 8vh;
  height: 8vh;
  border: 3px solid white;
  color: white;
  border-radius: 100px;
  font-weight: 700;
  font-size: 4vh;
  font-family: Montserrat;
}
