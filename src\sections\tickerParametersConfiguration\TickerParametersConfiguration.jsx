import HeadSectionComponent from "../../components/SectionTable/HeadSection"
import React, { useEffect, useState } from "react"
import {
  deletePublicity,
  getAllParameters,
  getAllPublicity,
  updateParameters,
  createPublicity,
} from "./services/api"
import MessageEventQuantity from "./components/messageEventQuantity"
import { Grid } from "@mui/material"
import { makeStyles } from "@mui/styles"
import ParametersFields from "./components/parametersFields"
import PublicityField from "./components/publicityField"
import PrimaryButton from "../../components/PrimaryButton"
import { useApp } from "../../AppProvider"
import TickerMonumental from "../Ticker/Ticker"

const useStyles = makeStyles(() => ({
  containerStyle: {
    direction: "column",
    maxWidth: "1008px",
    height: "400px",
    padding: "32px 43px 0px 42px",
    borderRadius: "15px",
    border: "1px solid #000000",
    justifyContent: "space-between",
  },
}))

const customHeaderStyles = makeStyles(() => ({
  container: {
    marginBottom: "40px !important",
  },
}))

const TickerParametersConfiguration = () => {
  const classes = useStyles()
  const headerClasses = customHeaderStyles()

  const { showNotification } = useApp()

  const [errors, setErrors] = useState({
    SeparatorError: "",
    TotalDurationError: "",
    SmsDurationError: "",
    RepetitionsError: "",
  })

  const [parameters, setParameters] = useState({
    speed: null,
    separator: null,
    separatorHeight: null,
    separatorWidth: null,
    model: null,
    shutdown: null,
    maxRepetitions: null,
    closeHour: null,
    eventDurationMinutes: null,
    size: null,
  })

  const [publicity, setPublicity] = useState([])
  const [newPublicity, setNewPublicity] = useState([])
  const [deletedPublicity, setDeletedPublicity] = useState([])

  const [smsDuration, setSmsDuration] = useState(
    (14 * 100) / (parameters.speed || 100)
  )

  useEffect(() => {
    setSmsDuration(parseInt((14 * 100) / (parameters.speed || 100)))
  }, [parameters.speed])

  const handleUpdateParameters = async () => {
    try {
      if (
        errors.SeparatorError != "" ||
        errors.TotalDurationError != "" ||
        errors.SmsDurationError != "" ||
        errors.RepetitionsError != ""
      )
        return
      await updateParameters({
        ...parameters,
        speed: Math.floor((14 * 100) / smsDuration),
      })
      if (deletedPublicity.length > 0) {
        await deletePublicity(deletedPublicity)
        setDeletedPublicity([])
      }
      if (newPublicity.length > 0) {
        await createPublicity(newPublicity.map((item) => item.message))
        setNewPublicity([])
        handleFetchPublicity()
      }
      showNotification("Parámetros actualizados exitosamente", "success")
    } catch (error) {
      showNotification(
        "Lo sentimos, se ha producido un error inesperado al actualizar los parámetros",
        "error"
      )
    }
  }

  const handleFetchParameters = async () => {
    try {
      const response = await getAllParameters()
      const parameters = response.data
      setParameters(parameters)
    } catch (error) {
      showNotification(
        "Lo sentimos, se ha producido un error inesperado al obtener los parámetros",
        "error"
      )
    }
  }
  const handleFetchPublicity = async () => {
    try {
      const response = await getAllPublicity()
      const publicity = response.data
      setPublicity(publicity)
    } catch (error) {
      showNotification(
        "Lo sentimos, se ha producido un error inesperado al obtener la publicidad",
        "error"
      )
    }
  }

  useEffect(() => {
    handleFetchParameters()
    handleFetchPublicity()
  }, [])

  return (
    <React.Fragment>
      <TickerMonumental
        tickerCS={{
          position: "absolute",
          bottom: "27px",
          left: "0px",
          width: "100%",
        }}
      />
      <Grid
        container
        direction={"column"}
        margin={"24px 0 0 64px"}
        overflow={"hidden"}
        maxWidth={"1008px"}
      >
        <HeadSectionComponent
          title={"Configuración de parámetros"}
          subTitle={
            "Administra los valores para configurar la visualización del cintillo"
          }
          tooltipTitle={"Solicitar campaña"}
          showAddButton={false}
          onAddButtonClick={() => {}}
          customStyle={{ container: headerClasses.container }}
        />
        <MessageEventQuantity
          eventDuration={parseInt(parameters.eventDurationMinutes) || 90}
          smsDuration={smsDuration > 0 ? smsDuration : 1}
        />

        <Grid container direction={"column"} className={classes.containerStyle}>
          <Grid item container width={"fit-content"} gap={"35px"} wrap="nowrap">
            <ParametersFields
              parameters={parameters}
              setParameters={setParameters}
              errors={errors}
              setErrors={setErrors}
              smsDuration={smsDuration}
              setSmsDuration={setSmsDuration}
            />
            <PublicityField
              publicity={publicity}
              setPublicity={setPublicity}
              newPublicity={newPublicity}
              setNewPublicity={setNewPublicity}
              deletedPublicity={deletedPublicity}
              setDeletedPublicity={setDeletedPublicity}
            />
          </Grid>
          <Grid
            item
            container
            width={"fit-content"}
            position={"relative"}
            right={"190px"}
            top={"290px"}
          >
            <PrimaryButton
              text="Guardar parametros"
              action={handleUpdateParameters}
              width="190px"
              height="41px"
              fontSize="14"
              lineHeight="28"
              type="submit"
              noShadow={true}
              cs={{
                borderRadius: "30px",
                ...((errors.SeparatorError != "" ||
                  errors.TotalDurationError != "" ||
                  errors.SmsDurationError != "" ||
                  errors.RepetitionsError != "") && {
                  background: "#98e4f0",
                  border: "2px solid #98e4f0",
                  cursor: "not-allowed",
                }),
              }}
            />
          </Grid>
        </Grid>
      </Grid>
    </React.Fragment>
  )
}

export default TickerParametersConfiguration
