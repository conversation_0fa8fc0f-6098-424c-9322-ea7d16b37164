import React, { useState, useCallback, useEffect } from 'react'
import { useApp } from "../../../../AppProvider";
import { makeStyles } from "@mui/styles";
import { useNavigate } from "react-router";
import {
  Popper, Fade, List, ListItemButton, ListItemText, Paper
} from "@mui/material";
import { getCampaignByUser, updateCampaignStatus, stopCampaign, cancelCampaign, getTrivia , startTriviaById, 
  restarTriviaById, resumeTriviaById, pauseTriviaById} from '../../services/api';
import { breakPointObserver } from '../../../../helpers/functions/functions';
import HeadSectionComponent from '../../../../components/SectionTable/HeadSection';
import TableComponent from '../../../../components/SectionTable/Table';
import ConfirmDialog from '../../../../components/ConfirmDialog/ConfirmDialog'
import { menuActionsByStatus } from "../../constants/tableMenuByStatus";
import { io } from 'socket.io-client';
import { TriviaSocketEvents } from '../../socket-events/trivia-socket-events';

const SOCKET_SERVER_URL = process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH; // La URL de tu backend NestJS (donde escucha el servidor de Nest)

const useStyles = makeStyles(() => ({
    tableTitle: {
      marginLeft: 5,
      fontFamily: "Ubuntu !important",
      fontStyle: "normal !important",
      fontWeight: "700 !important",
      fontSize: "30px !important",
      lineHeight: "24px",
      letterSpacing: "0.15px",
      color: "rgba(78, 85, 137, 1)",
      marginBottom: "40px",
    },
    tableCellText: {
      fontSize: "18px !important",
      fontWeight: "700 !important",
      fontFamily: "Ubuntu !important",
      marginLeft: "-18px !important",
    },
    clickableCellText: {
      color: "#0957A0",
    },
    newCampaignButton: {
      height: "44px",
      width: "212px",
      fontSize: "20px !important",
      fontFamily: "Ubuntu !important",
      backgroundColor: "#008C0D !important",
      fontWeight: "bold !important",
      borderRadius: "8px !important",
      textTransform: "none !important",
      letterSpacing: "0.15px !important",
      lineHeight: "28px !important",
      "@media screen and (maxWidth: 400px)": {
        fontSize: "0.4rem !important",
      },
    },
    campaignMenuItem: {
      height: 32,
      textAlign: "center !important",
      "&:hover": {
        background: "rgba(9, 87, 160, 0.15) !important",
        color: "#0957A0",
      },
    },
    campaignMenuItemText: {
      fontSize: 12,
      fontFamily: "Ubuntu",
      color: "inherit",
    },
    paginationLabel: {
      fontFamily: "Ubuntu",
      fontStyle: "normal",
      fontWeight: "600",
      fontSize: "16px",
      lineHeight: "24px",
      letterSpacing: "0.15px",
      color: "#737373",
      "@media screen and (max-width: 1350px)": {
        fontSize: "12px !important",
      },
    },
    customTooltip: {
      "& .MuiTooltip-popper": {
        backgroundColor:'red !important',
        color:'blue !important'
      }
      
    }
}));

//HEADERS DE TABLA 
const headCells = [
  {
    id: 'title',
    label: 'Titulo',
    numeric: false,
  },
  {
    id: 'product',
    label: 'Producto',
    numeric: false,
  },
  {
    id: 'winners',
    label: 'Ganadores',
    numeric: true,
  },
  {
    id: 'status',
    label: 'Estado',
    numeric: false,
  },
];

const columOrderToShow = [
  {
    name:'title',
    length:false,
    link:false
  },
  {
    name:'product',
    length:false,
    link:false

  },
  {
    name:'winners',
    length:false,
    link:false
  },
  {
    name:'status',
    length:false,
    link:false
  }
]

const TableCampaignData = (props) => {
  

  const classes = useStyles();
  const navigate = useNavigate();
  const { handleRefresh, refresh, currentUser, showNotificationV2,setSearchOptions, searchOptions } = useApp();

  const [campaignList, setCampaignList] = useState([]);
  const [filteredCampaignList, setFilteredCampaignList] = useState(campaignList);
  
  const [campaignMenuEl, setCampaignMenuEl] = useState(null);
  const [openCampaignMenu, setOpenCampaignMenu] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState({});
  //CONFIRM MODAL PROPS
  const [modalConfig,setModalConfig] = useState({
    title:'',
    message:'',
    nextButtonAction:null,
  })
  // CONFIG MENU BY STATUS 

  const [displayedMenu, setDisplayedMenu] = useState({
    status:'',
    displayOptions: [],
    disable:{}
  })

  const [searchInput, setSearchInput] = useState("");
  const [openConfirm, setOpenConfirm] = useState(false);
  /*ENHANCED HEAD*/
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("title");
  /*TABLE PAGINATION*/
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [page, setPage] = useState(0);
  const [loading,setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('Conectando...');

  const notificationConfig = {asOrigin:{vertical:"top", horizontal:"right"},asMargin:'160px -16px 0px 0px'};

  const [breakPoint, setBreakPoint] = useState('');


    useEffect(() => {
      // 1. Conectar al servidor Socket.IO
      // Asegúrate de que el puerto aquí sea el puerto de tu aplicación NestJS,
      // no necesariamente el puerto del gateway si no lo has configurado en un puerto separado.
      const socket = io(SOCKET_SERVER_URL);
  
      // 2. Manejar eventos de conexión
      socket.on('connect', () => {
        console.log('Conectado al servidor WebSocket con ID:', socket.id);
        setConnectionStatus('Conectado');
      });

      socket.on('disconnect', () => {
        console.log('Desconectado del servidor WebSocket');
        setConnectionStatus('Desconectado');
      });
  
      socket.on('connect_error', (error) => {
        console.log('Error de conexión:',  process.env.REACT_APP_MONUMENTAL_TRIVIA_WS_PATH);
        console.error('Error de conexión:', error.message);
        setConnectionStatus(`Error de conexión: ${error.message}`);
      });
  
      socket.on(TriviaSocketEvents.BEGIN_TRIVIA, (data) => {
        fetchCampaign();
      });
      socket.on(TriviaSocketEvents.IN_PROGRESS_TRIVIA, (data) => {
        fetchCampaign();
      });
       socket.on(TriviaSocketEvents.FINISH_TRIVIA, (data) => {
        fetchCampaign();
      });
  
      // 6. Limpiar listeners y desconectar cuando el componente se desmonta
      return () => {
        socket.off('connect');
        socket.off('disconnect');
        socket.off('connect_error');
        socket.off(TriviaSocketEvents.BEGIN_TRIVIA);
        socket.off(TriviaSocketEvents.IN_PROGRESS_TRIVIA);
        socket.off(TriviaSocketEvents.FINISH_TRIVIA);
        socket.disconnect();
      };
    }, []); 


  useEffect (() => {
    breakPointObserver(setBreakPoint)
  }, [breakPoint])

  useEffect (async () => {
    if(refresh === true){
      handleRefresh(false);
      fetchCampaign();
    }
  }, [refresh]);

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleOpenActionMenu = (campaign) => (event) => {
    setDisplayedMenu(menuActionsByStatus.find(menuActions => menuActions.status === campaign.status.toUpperCase()))
    setSelectedCampaign(campaign);
    setCampaignMenuEl(event.currentTarget);
    setOpenCampaignMenu((prev) => selectedCampaign !== campaign || !prev);
  };

  const clearCampaignMenu = () => {
    setSelectedCampaign({});
    setCampaignMenuEl(null);
    setOpenCampaignMenu(false);
  }

  const handleChangePage = (event, newPage) => {
    clearCampaignMenu();
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleNavigate = (path) => {
  /*   setSearchOptions({"campaigns":{orderBy,order,searchInput}}); */
    navigate(path);
  }

  const onNewCampaignClick = () => {
    handleNavigate('/trivia/new');
  }

  const searchInputChange = (event) => {
    setSearchInput(event.target.value);
    setCampaignMenuEl(null);
    setOpenCampaignMenu(false);
    setPage(0);
  };

  const onDelete = () => {
    setSearchInput("");
  };

  const fetchCampaign = useCallback(async () => {
    const userCampaigns = await getTrivia();

    setLoading(false);
    setCampaignList(userCampaigns.data)
  }, []);

  
  const applyFilter = (data,filterValue) => {
  
    let filteredList = [];
    if(filterValue === ''){
      filteredList = data;
    }
    else if(filterValue !== ''){
      const alteredValue = filterValue.toLowerCase();
        filteredList = data.filter((campaign) => {
          return (
            campaign.title.toLowerCase().includes(alteredValue) || 
            campaign.product.toLowerCase().includes(alteredValue) ||
            campaign.status.toLowerCase().includes(alteredValue)
        )});
    }
    return filteredList;
  } 

  useEffect(() => {
    if(searchOptions !== null && searchOptions['campaigns'] !== undefined){
      const {order,orderBy,searchInput} = searchOptions['campaigns'];
      setOrder(order);
      setOrderBy(orderBy);
      setSearchInput(searchInput);
    }
  }, [searchOptions]);

  useEffect(() => {
    fetchCampaign();
  }, [fetchCampaign]);

  useEffect(() => {
    setFilteredCampaignList(applyFilter(campaignList,searchInput));
  }, [campaignList,searchInput]);


    return (
    <React.Fragment>
      <HeadSectionComponent
          title={'Trivias'}
          subTitle={'<p> <b>Crea y visualiza</b> el estado de tus Trivias.</p>'}
          tooltipTitle={'Solicitar trivia'}
          showAddButton={true}
          onAddButtonClick={onNewCampaignClick}
        />
        <TableComponent
          showSearchInput={true}
          searchInputConfig={{
            searchInputValue:searchInput,
            onChangeSearchInput:searchInputChange,
            onClearSearchInput:onDelete,
            searchInputPlaceHolder:'Buscar'
          }}
          tableHeaderProps={{
            order:order,
            orderBy:orderBy,
            onRequestSort:handleRequestSort,
            headCells:headCells,
            showActionCell:true
          }}
          filteredData={filteredCampaignList}
          noFoundDataLabel={'Lo sentimos, no hay resultados para tu búsquedas.'}
          isLoading={loading}
          orderOfColumnsToDisplay={columOrderToShow}
          showMenuColum={true}
          menuColumConfig={{
            displayOrder:['hola','adios'],
            onOpenMenu:handleOpenActionMenu,
          }}
          paginationConfig={{
            onPageChange: handleChangePage,
            onRowsPerPageChange :handleChangeRowsPerPage,
            page:page,
            rowsPerPage:rowsPerPage,
            rowsPerPageLabel:'Registro por páginas:',
            rowsPerPageSequence: [5, 10, 15]
          }}
        />
      <Popper
        id="popper"
        open={openCampaignMenu}
        anchorEl={campaignMenuEl}
        placement="left-start"
        transition
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <Paper>
              <List
                sx={{ minWidth: 113, padding: 0 }}
                component="nav"
                aria-label="main mailbox folders"
              >
                {displayedMenu.displayOptions.map((value, index) => {
                   return  (<ListItemButton
                    key={index}
                    className={classes.campaignMenuItem}
                    sx={{
                      borderBottom: "1px solid #737373",
                    }}
                    onClick={() => {
                    if(!value.modal){
                      if(value.name !== 'Preview'){
                        handleNavigate(value.action(selectedCampaign.id));
                      }else{
                        if(selectedCampaign.product == 'tribu'){
                           window.open(value.action(selectedCampaign.id), '_blank');
                        }else {
                            window.open(`/previewaq/${selectedCampaign.id}`, '_blank');
                        }
                     
                      }
                     clearCampaignMenu();
                    }else {
                      setModalConfig({
                        title: value.modalConfig.title,
                        message: value.modalConfig.message(selectedCampaign.title),
                        nextButtonAction:  async () => {
                          if(value.name === 'Iniciar'){
                          await  startTriviaById(selectedCampaign.id)
                          }else if(value.name === 'Pausar'){
                           await pauseTriviaById(selectedCampaign.id)
                          }else if(value.name === 'Reanudar'){
                           await resumeTriviaById(selectedCampaign.id)
                          }else if(value.name === 'Reiniciar'){
                           await restarTriviaById(selectedCampaign.id)
                          }

                          fetchCampaign();
                          setOpenConfirm(false);
                       
                         clearCampaignMenu();
                         
                        },
                      });
                      
                      setOpenConfirm(true);
                    }
                    }}
                  >
                    <ListItemText
                      disableTypography={true}
                      className={classes.campaignMenuItemText}
                      primary={<span>{value.name}</span>}
                    />
                  </ListItemButton>)
                })
              }
              </List>
            </Paper>
          </Fade>
        )}
      </Popper>
      <ConfirmDialog
        shouldOpen={openConfirm}
        nextButtonLabel={'Si'}
        backButtonLabel={"No"}
        backButtonAction={() => {
          setOpenConfirm(false);
          setModalConfig({
            title: '',
            message: '',
            nextButtonAction: null,
          });
        }}
        {...modalConfig}
        />
    </React.Fragment>
    )
}

TableCampaignData.propTypes = {

}

export default TableCampaignData
