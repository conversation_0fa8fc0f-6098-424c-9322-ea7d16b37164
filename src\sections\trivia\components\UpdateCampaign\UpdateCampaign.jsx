import React from 'react'
import { useState , useEffect} from 'react';
import {  getTriviaById} from '../../services/api';
import { useLocation } from "react-router";
import CampaignForm from '../CampaignForm/CampaignForm';
import TriviaUpdateForm from '../TriviaUpdateForm/TriviaUpdateForm';


const UpdateCampaign = (props) => {

    const location = useLocation()

    const [campaign, setCampaign] = useState(null)
    const [loading, setLoading] = useState(true)

    useEffect(async () => {
      setLoading(true)
      const idCampaign = location.pathname.split('trivia/update/')[1];
      const {data} = await getTriviaById(idCampaign)
      console.log("Campaign data:", data);
      setCampaign(data)  
      setLoading(false)
    }, []);

   
    return (
      <React.Fragment>
        {!loading &&
         <TriviaUpdateForm 
            formType={'edit'} 
            campaignInfo={campaign}
           disableInputs={{
              disableBusiness: campaign ? true : false,
          }}
          />
        }
        
      </React.Fragment>
      );
    }

UpdateCampaign.propTypes = {

}

export default UpdateCampaign
