import { useEffect, useState } from "react"
import { useFetchTickerParameters } from "../../../Ticker/custom-hooks/fetch-ticker-parameters-hook"
import { useApp } from "../../../../AppProvider"
import { makeStyles } from "@mui/styles"
import { toggleShutdownTicker } from "../../api/api"
import { createSvgIcon } from "@mui/material"

const OnIcon = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="27"
    height="26"
    viewBox="0 0 27 26"
    fill="none"
  >
    <rect
      x="0.882812"
      y="0.171875"
      width="25.6571"
      height="25.6571"
      rx="10"
      fill="none"
    />
    <path
      d="M6.22793 22.6217C5.63996 22.6217 5.13662 22.4124 4.71791 21.9937C4.2992 21.5749 4.08984 21.0716 4.08984 20.4836V5.517C4.08984 4.92902 4.2992 4.42568 4.71791 4.00697C5.13662 3.58826 5.63996 3.37891 6.22793 3.37891H21.1946C21.7825 3.37891 22.2859 3.58826 22.7046 4.00697C23.1233 4.42568 23.3327 4.92902 23.3327 5.517V20.4836C23.3327 21.0716 23.1233 21.5749 22.7046 21.9937C22.2859 22.4124 21.7825 22.6217 21.1946 22.6217H6.22793ZM6.22793 20.4836H21.1946V7.65509H6.22793V20.4836ZM13.7112 18.3455C12.2502 18.3455 10.9451 17.9491 9.79587 17.1562C8.64665 16.3633 7.81368 15.3344 7.29698 14.0694C7.81368 12.8043 8.64665 11.7754 9.79587 10.9825C10.9451 10.1896 12.2502 9.79318 13.7112 9.79318C15.1723 9.79318 16.4774 10.1896 17.6266 10.9825C18.7758 11.7754 19.6088 12.8043 20.1255 14.0694C19.6088 15.3344 18.7758 16.3633 17.6266 17.1562C16.4774 17.9491 15.1723 18.3455 13.7112 18.3455ZM13.7112 16.742C14.709 16.742 15.6177 16.5059 16.4373 16.0337C17.2569 15.5616 17.8983 14.9068 18.3616 14.0694C17.8983 13.2319 17.2569 12.5771 16.4373 12.105C15.6177 11.6328 14.709 11.3967 13.7112 11.3967C12.7135 11.3967 11.8048 11.6328 10.9852 12.105C10.1656 12.5771 9.52416 13.2319 9.0609 14.0694C9.52416 14.9068 10.1656 15.5616 10.9852 16.0337C11.8048 16.5059 12.7135 16.742 13.7112 16.742ZM13.7112 15.6729C14.1567 15.6729 14.5353 15.517 14.8471 15.2052C15.1589 14.8934 15.3148 14.5148 15.3148 14.0694C15.3148 13.6239 15.1589 13.2453 14.8471 12.9335C14.5353 12.6217 14.1567 12.4658 13.7112 12.4658C13.2658 12.4658 12.8872 12.6217 12.5754 12.9335C12.2636 13.2453 12.1077 13.6239 12.1077 14.0694C12.1077 14.5148 12.2636 14.8934 12.5754 15.2052C12.8872 15.517 13.2658 15.6729 13.7112 15.6729Z"
      fill="#3DB76E"
    />
  </svg>,
  "on_icon"
)

const OffIcon = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="27"
    height="26"
    viewBox="0 0 27 24"
    fill="none"
  >
    <rect x="0.882812"
      y="0.171875"
      width="25.6571"
      height="25.6571"
      rx="10" stroke="none" />
    <path
      d="M19.7734 22.5998L18.1734 20.9998H4.99844C4.44844 20.9998 3.9776 20.804 3.58594 20.4123C3.19427 20.0206 2.99844 19.5498 2.99844 18.9998V5.82481L1.39844 4.1998L2.79844 2.7998L21.1984 21.1998L19.7734 22.5998ZM4.99844 18.9998H16.1734L4.99844 7.82481V18.9998ZM20.9984 18.1748L18.9984 16.1748V7.9998H10.8234L5.82344 2.9998H18.9984C19.5484 2.9998 20.0193 3.19564 20.4109 3.5873C20.8026 3.97897 20.9984 4.4498 20.9984 4.9998V18.1748Z"
      fill="#CD252D"
    />
  </svg>,
  "off_icon"
)

const useStyles = makeStyles(() => ({
  shutdownButton: {
    borderRadius: "10px",
    background: "white",
    "&:hover": {
      cursor: "pointer",
    },
    width: "96px",
    height: "28px",
    fontFamily: "Inter",
    fontSize: "14px",
    fontWeight: "700",
    textTransform: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "3px",
  },
  tickerOnButton: {
    border: "1px solid #3DB76E",
    color: "#3DB76E",
  },
  tickerOffButton: {
    border: "1px solid #CD252D",
    color: "#CD252D",
  },
}))

const ShutdownTickerButton = () => {
  const classes = useStyles()
  const { showNotification } = useApp()
  const [isShutdown, setIsShutdown] = useState(false)
  const [fetchParams, setFetchParams] = useState(true)
  const { tickerParameters, setTickerParameters } =
    useFetchTickerParameters(fetchParams)

  useEffect(() => {
    setIsShutdown(tickerParameters.shutdown)
  }, [tickerParameters])

  const handleShutdown = async () => {
    try {
      const response = await toggleShutdownTicker()
      showNotification(`El servicio de SMS fue ${ isShutdown ? "activado" : "apagado"} exitosamente.`, "success")
      setIsShutdown((prev) => !prev)
    } catch (error) {
      showNotification(
        "Ha ocurrido un error al cambiar el estado del ticker",
        "error"
      )
    }
  }

  return (
    <button
      className={
        classes.shutdownButton +
        " " +
        (isShutdown ? classes.tickerOnButton : classes.tickerOffButton)
      }
      onClick={handleShutdown}
    >
      {isShutdown ? (
        <OnIcon sx={{ marginBottom: "2px" }} />
      ) : (
        <OffIcon sx={{ marginBottom: "2px" }} />
      )}
      {isShutdown ? "START" : "STOP"}
    </button>
  )
}

export default ShutdownTickerButton
